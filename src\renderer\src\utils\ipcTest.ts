import { IPCService } from '../services/ipcService'

export class IPCTest {
  static async testAllChannels(): Promise<{ [key: string]: boolean }> {
    const results: { [key: string]: boolean } = {}

    try {
      // Test app version
      const appInfo = await IPCService.getAppVersion()
      results['GET_APP_VERSION'] = !!(appInfo.version && appInfo.name)
    } catch (error) {
      results['GET_APP_VERSION'] = false
      console.error('GET_APP_VERSION failed:', error)
    }

    try {
      // Test system info
      const systemInfo = await IPCService.getSystemInfo()
      results['GET_SYSTEM_INFO'] = !!(systemInfo.platform && systemInfo.arch)
    } catch (error) {
      results['GET_SYSTEM_INFO'] = false
      console.error('GET_SYSTEM_INFO failed:', error)
    }

    try {
      // Test settings
      const settings = await IPCService.getSettings()
      results['GET_SETTINGS'] = !!(settings.outputPath && settings.theme)
    } catch (error) {
      results['GET_SETTINGS'] = false
      console.error('GET_SETTINGS failed:', error)
    }

    try {
      // Test supported formats
      const formats = await IPCService.getSupportedFormats()
      results['GET_SUPPORTED_FORMATS'] = !!(formats.VIDEO && formats.AUDIO && formats.IMAGE)
    } catch (error) {
      results['GET_SUPPORTED_FORMATS'] = false
      console.error('GET_SUPPORTED_FORMATS failed:', error)
    }

    return results
  }

  static async logSystemInfo(): Promise<void> {
    try {
      const systemInfo = await IPCService.getSystemInfo()
      console.log('System Information:', {
        platform: systemInfo.platform,
        architecture: systemInfo.arch,
        release: systemInfo.release,
        totalMemory: `${(systemInfo.totalMemory / 1024 / 1024 / 1024).toFixed(2)} GB`,
        freeMemory: `${(systemInfo.freeMemory / 1024 / 1024 / 1024).toFixed(2)} GB`,
        cpus: systemInfo.cpus,
        homeDirectory: systemInfo.homedir,
        tempDirectory: systemInfo.tmpdir
      })
    } catch (error) {
      console.error('Failed to get system info:', error)
    }
  }

  static async testWindowControls(): Promise<void> {
    console.log('Testing window controls...')
    
    // Note: These would actually control the window, so we just log them
    console.log('Window control methods available:')
    console.log('- IPCService.minimizeWindow()')
    console.log('- IPCService.maximizeWindow()')
    console.log('- IPCService.closeWindow()')
  }

  static async runFullTest(): Promise<void> {
    console.log('🚀 Starting IPC Communication Test...')
    
    // Test all channels
    const channelResults = await this.testAllChannels()
    
    console.log('📊 IPC Channel Test Results:')
    Object.entries(channelResults).forEach(([channel, success]) => {
      console.log(`${success ? '✅' : '❌'} ${channel}`)
    })
    
    // Log system info
    await this.logSystemInfo()
    
    // Test window controls info
    await this.testWindowControls()
    
    const successCount = Object.values(channelResults).filter(Boolean).length
    const totalCount = Object.keys(channelResults).length
    
    console.log(`🎯 Test Summary: ${successCount}/${totalCount} channels working`)
    
    if (successCount === totalCount) {
      console.log('🎉 All IPC channels are working correctly!')
    } else {
      console.log('⚠️ Some IPC channels have issues. Check the logs above.')
    }
  }
}