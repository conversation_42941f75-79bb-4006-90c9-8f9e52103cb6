import * as path from 'path'
import * as os from 'os'
import { shell } from 'electron'
import { DEFAULT_PATHS } from '../../shared/constants'
import { VideoConverter } from '../converters/videoConverter'
import { AudioConverter } from '../converters/audioConverter'
import { ImageConverter } from '../converters/imageConverter'
import { HeicConverter } from '../converters/heicConverter'
import { PathUtils } from '../utils/pathUtils'
import { ProgressManager } from '../utils/progressManager'
import type { ConversionTask, ConversionOptions, SupportedFormat, FileType } from '../../shared/types'

export class ConversionService {
  private static activeTasks = new Map<string, ConversionTask>()
  private static initialized = false

  static async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      // Initialize all converters
      await VideoConverter.initialize()
      await AudioConverter.initialize()
      await ImageConverter.initialize()
      await He<PERSON><PERSON>onverter.initialize()

      this.initialized = true
      console.log('ConversionService initialized successfully')
    } catch (error) {
      console.error('Failed to initialize ConversionService:', error)
      throw error
    }
  }

  static async convertFile(
    inputPath: string,
    outputPath: string,
    inputFormat: SupportedFormat,
    outputFormat: SupportedFormat,
    fileType: FileType,
    options?: ConversionOptions
  ): Promise<ConversionTask> {
    // Ensure service is initialized
    if (!this.initialized) {
      await this.initialize()
    }

    const taskId = this.generateTaskId()
    
    // Generate full output path
    const fullOutputPath = path.isAbsolute(outputPath) 
      ? outputPath 
      : path.join(this.getDefaultOutputPath(), outputPath)
    
    const task: ConversionTask = {
      id: taskId,
      inputPath,
      outputPath: fullOutputPath,
      inputFormat,
      outputFormat,
      fileType,
      options,
      status: 'processing',
      progress: 0,
      startTime: new Date()
    }

    this.activeTasks.set(taskId, task)

    // Create progress tracking
    const progressManager = ProgressManager.getInstance()
    const fileName = path.basename(inputPath)
    progressManager.createConversion(taskId, fileName, inputPath, fullOutputPath)

    try {
      // Perform actual conversion based on file type
      await this.performConversion(task, taskId)
      
      task.status = 'completed'
      task.progress = 100
      task.endTime = new Date()
      
    } catch (error) {
      task.status = 'error'
      task.error = error instanceof Error ? error.message : 'Unknown error'
      console.error(`Conversion failed for task ${taskId}:`, error)
    }

    return task
  }

  private static async performConversion(task: ConversionTask, progressId: string): Promise<void> {
    const { inputPath, outputPath, inputFormat, outputFormat, fileType, options } = task

    switch (fileType) {
      case 'VIDEO':
        await VideoConverter.convert(
          inputPath,
          outputPath,
          inputFormat,
          outputFormat,
          options,
          progressId
        )
        break

      case 'AUDIO':
        await AudioConverter.convert(
          inputPath,
          outputPath,
          inputFormat,
          outputFormat,
          options,
          progressId
        )
        break

      case 'IMAGE':
        await ImageConverter.convert(
          inputPath,
          outputPath,
          inputFormat,
          outputFormat,
          options,
          progressId
        )
        break

      default:
        throw new Error(`Unsupported file type: ${fileType}`)
    }
  }

  static async getConversionProgress(taskId: string): Promise<number> {
    const task = this.activeTasks.get(taskId)
    return task?.progress || 0
  }

  static async cancelConversion(taskId: string): Promise<void> {
    const task = this.activeTasks.get(taskId)
    if (task) {
      task.status = 'cancelled'
      this.activeTasks.delete(taskId)
    }
  }

  static async openOutputFolder(folderPath?: string): Promise<void> {
    const outputPath = folderPath || this.getDefaultOutputPath()
    
    // Ensure the directory exists
    PathUtils.ensureDirectoryExists(outputPath)
    
    await shell.openPath(outputPath)
  }

  static getDefaultOutputPath(): string {
    return path.join(os.homedir(), 'Desktop', DEFAULT_PATHS.OUTPUT_FOLDER)
  }

  static getActiveTasks(): ConversionTask[] {
    return Array.from(this.activeTasks.values())
  }

  static getTaskById(taskId: string): ConversionTask | undefined {
    return this.activeTasks.get(taskId)
  }

  static clearCompletedTasks(): void {
    for (const [taskId, task] of this.activeTasks.entries()) {
      if (task.status === 'completed' || task.status === 'error' || task.status === 'cancelled') {
        this.activeTasks.delete(taskId)
      }
    }
  }

  private static generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}