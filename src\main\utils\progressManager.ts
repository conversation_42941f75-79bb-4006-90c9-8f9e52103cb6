import { EventEmitter } from 'events'
import { BrowserWindow } from 'electron'
// Progress manager for tracking conversion progress

export interface ConversionProgress {
  id: string
  fileName: string
  inputPath: string
  outputPath: string
  status: 'idle' | 'processing' | 'completed' | 'error' | 'cancelled'
  progress: number // 0-100
  currentTime?: string
  totalTime?: string
  speed?: string
  fps?: number
  bitrate?: string
  size?: string
  eta?: string
  error?: string
  startTime: number
  endTime?: number
}

export class ProgressManager extends EventEmitter {
  private static instance: ProgressManager
  private conversions: Map<string, ConversionProgress> = new Map()
  private mainWindow: BrowserWindow | null = null

  private constructor() {
    super()
  }

  static getInstance(): ProgressManager {
    if (!ProgressManager.instance) {
      ProgressManager.instance = new ProgressManager()
    }
    return ProgressManager.instance
  }

  setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window
  }

  createConversion(
    id: string,
    fileName: string,
    inputPath: string,
    outputPath: string
  ): ConversionProgress {
    const conversion: ConversionProgress = {
      id,
      fileName,
      inputPath,
      outputPath,
      status: 'idle',
      progress: 0,
      startTime: Date.now()
    }

    this.conversions.set(id, conversion)
    this.notifyProgress(conversion)
    return conversion
  }

  updateProgress(id: string, updates: Partial<ConversionProgress>): void {
    const conversion = this.conversions.get(id)
    if (!conversion) return

    Object.assign(conversion, updates)
    this.notifyProgress(conversion)
    this.emit('progress', conversion)
  }

  startConversion(id: string): void {
    this.updateProgress(id, {
      status: 'processing',
      startTime: Date.now()
    })
  }

  completeConversion(id: string): void {
    this.updateProgress(id, {
      status: 'completed',
      progress: 100,
      endTime: Date.now()
    })
  }

  errorConversion(id: string, error: string): void {
    this.updateProgress(id, {
      status: 'error',
      error,
      endTime: Date.now()
    })
  }

  cancelConversion(id: string): void {
    this.updateProgress(id, {
      status: 'cancelled',
      endTime: Date.now()
    })
  }

  getConversion(id: string): ConversionProgress | undefined {
    return this.conversions.get(id)
  }

  getAllConversions(): ConversionProgress[] {
    return Array.from(this.conversions.values())
  }

  getActiveConversions(): ConversionProgress[] {
    return this.getAllConversions().filter(c => c.status === 'processing')
  }

  removeConversion(id: string): void {
    this.conversions.delete(id)
    this.notifyRemoval(id)
  }

  clearCompleted(): void {
    const completedIds: string[] = []
    this.conversions.forEach((conversion, id) => {
      if (conversion.status === 'completed' || conversion.status === 'error') {
        completedIds.push(id)
      }
    })

    completedIds.forEach(id => {
      this.conversions.delete(id)
    })

    this.notifyBulkRemoval(completedIds)
  }

  clearAll(): void {
    const allIds = Array.from(this.conversions.keys())
    this.conversions.clear()
    this.notifyBulkRemoval(allIds)
  }

  private notifyProgress(conversion: ConversionProgress): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('conversion-progress-update', conversion)
    }
  }

  private notifyRemoval(id: string): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('conversion-removed', id)
    }
  }

  private notifyBulkRemoval(ids: string[]): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('conversions-bulk-removed', ids)
    }
  }

  // 计算转换统计信息
  getConversionStats(id: string): {
    duration: number
    averageSpeed: number
    estimatedTimeRemaining?: number
  } | null {
    const conversion = this.conversions.get(id)
    if (!conversion) return null

    const now = Date.now()
    const duration = (conversion.endTime || now) - conversion.startTime
    const averageSpeed = conversion.progress / (duration / 1000) // progress per second

    let estimatedTimeRemaining: number | undefined
    if (conversion.status === 'processing' && conversion.progress > 0) {
      const remainingProgress = 100 - conversion.progress
      estimatedTimeRemaining = remainingProgress / averageSpeed
    }

    return {
      duration,
      averageSpeed,
      estimatedTimeRemaining
    }
  }

  // 格式化时间显示
  static formatTime(seconds: number): string {
    if (!seconds || !isFinite(seconds)) return '00:00'
    
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  // 格式化文件大小
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 格式化速度显示
  static formatSpeed(bytesPerSecond: number): string {
    return ProgressManager.formatFileSize(bytesPerSecond) + '/s'
  }
}