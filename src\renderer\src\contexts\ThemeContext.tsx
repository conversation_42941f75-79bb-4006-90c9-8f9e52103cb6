import React, { createContext, useContext, useEffect, useState } from 'react'
import { SettingsService } from '../services/settingsService'
import type { AppSettings } from '../../../shared/types'

interface ThemeContextType {
  theme: 'light' | 'dark'
  setTheme: (theme: 'light' | 'dark') => void
  isLoading: boolean
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

interface ThemeProviderProps {
  children: React.ReactNode
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setThemeState] = useState<'light' | 'dark'>('dark')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Load initial theme from settings
    const loadTheme = async () => {
      try {
        const settings = await SettingsService.getSettings()
        setThemeState(settings.theme)
        applyThemeToDocument(settings.theme)
      } catch (error) {
        console.error('Failed to load theme:', error)
        // Default to dark theme if loading fails
        setThemeState('dark')
        applyThemeToDocument('dark')
      } finally {
        setIsLoading(false)
      }
    }

    loadTheme()
  }, [])

  const applyThemeToDocument = (newTheme: 'light' | 'dark') => {
    const root = document.documentElement
    if (newTheme === 'dark') {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }
  }

  const setTheme = async (newTheme: 'light' | 'dark') => {
    try {
      // Update settings
      await SettingsService.updateSettings({ theme: newTheme })
      
      // Update local state
      setThemeState(newTheme)
      
      // Apply theme to document
      applyThemeToDocument(newTheme)
    } catch (error) {
      console.error('Failed to update theme:', error)
    }
  }

  const value: ThemeContextType = {
    theme,
    setTheme,
    isLoading
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  )
}