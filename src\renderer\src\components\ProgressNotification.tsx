import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Eye } from 'lucide-react'

interface ConversionProgress {
  id: string
  fileName: string
  status: 'idle' | 'processing' | 'completed' | 'error' | 'cancelled'
  progress: number
  error?: string
}

interface ProgressNotificationProps {
  onShowDetails: () => void
}

const ProgressNotification: React.FC<ProgressNotificationProps> = ({ onShowDetails }) => {
  const [activeConversions, setActiveConversions] = useState<ConversionProgress[]>([])
  const [recentCompleted, setRecentCompleted] = useState<ConversionProgress[]>([])

  useEffect(() => {
    // Listen for progress updates
    const handleProgressUpdate = (_event: any, conversion: ConversionProgress) => {
      setActiveConversions(prev => {
        const index = prev.findIndex(c => c.id === conversion.id)
        if (conversion.status === 'processing') {
          if (index >= 0) {
            const updated = [...prev]
            updated[index] = conversion
            return updated
          } else {
            return [...prev, conversion]
          }
        } else {
          // Remove from active when completed/error
          const filtered = prev.filter(c => c.id !== conversion.id)

          // Add to recent completed if successful or error
          if (conversion.status === 'completed' || conversion.status === 'error') {
            setRecentCompleted(recent => {
              const newRecent = [conversion, ...recent.filter(c => c.id !== conversion.id)]
              return newRecent.slice(0, 3) // Keep only last 3
            })

            // Auto-remove from recent after 5 seconds if completed successfully
            if (conversion.status === 'completed') {
              setTimeout(() => {
                setRecentCompleted(recent => recent.filter(c => c.id !== conversion.id))
              }, 5000)
            }
          }

          return filtered
        }
      })
    }

    const handleConversionRemoved = (_event: any, conversionId: string) => {
      setActiveConversions(prev => prev.filter(c => c.id !== conversionId))
      setRecentCompleted(prev => prev.filter(c => c.id !== conversionId))
    }

    const handleBulkRemoved = (_event: any, conversionIds: string[]) => {
      setActiveConversions(prev => prev.filter(c => !conversionIds.includes(c.id)))
      setRecentCompleted(prev => prev.filter(c => !conversionIds.includes(c.id)))
    }

    // Add event listeners
    window.electron.ipcRenderer.on('conversion-progress-update', handleProgressUpdate)
    window.electron.ipcRenderer.on('conversion-removed', handleConversionRemoved)
    window.electron.ipcRenderer.on('conversions-bulk-removed', handleBulkRemoved)

    return () => {
      window.electron.ipcRenderer.removeListener('conversion-progress-update', handleProgressUpdate)
      window.electron.ipcRenderer.removeListener('conversion-removed', handleConversionRemoved)
      window.electron.ipcRenderer.removeListener('conversions-bulk-removed', handleBulkRemoved)
    }
  }, [])

  const totalConversions = activeConversions.length + recentCompleted.length

  if (totalConversions === 0) return null

  return (
    <div className="fixed bottom-4 right-4 z-40 space-y-2">
      {/* Active Conversions */}
      {activeConversions.map((conversion, index) => (
        <div
          key={conversion.id}
          className="bg-gray-900 bg-opacity-95 backdrop-blur-md border border-gray-700 hover:border-gray-600 rounded-lg p-4 min-w-80 shadow-glow-sm hover:shadow-glow transition-all duration-400 hover:scale-102 relative overflow-hidden"
        >
          {/* Background shimmer effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500 via-opacity-5 to-transparent animate-shimmer opacity-50"></div>

          <div className="relative z-10">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4 text-blue-400 animate-spin" />
                <span className="text-white font-medium text-sm truncate max-w-48" title={conversion.fileName}>
                  {conversion.fileName}
                </span>
              </div>
              <span className="text-blue-400 font-medium text-sm">
                {Math.round(conversion.progress)}%
              </span>
            </div>

            {/* Progress Bar */}
            <div className="w-full bg-gray-700 rounded-full h-2 mb-2 overflow-hidden shadow-inner">
              <div
                className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-500 relative overflow-hidden"
                style={{ width: `${Math.min(conversion.progress, 100)}%` }}
              >
                {/* Progress bar shimmer */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white via-opacity-30 to-transparent animate-shimmer"></div>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-400 text-xs">转换中...</span>
              <button
                onClick={onShowDetails}
                className="text-gray-400 hover:text-white transition-all duration-300 hover:scale-110 group"
                title="查看详细进度"
              >
                <Eye className="w-4 h-4 group-hover:scale-110 transition-transform duration-300" />
              </button>
            </div>
          </div>
        </div>
      ))}

      {/* Recent Completed */}
      {recentCompleted.map((conversion, index) => (
        <div
          key={conversion.id}
          className={`bg-gray-900 bg-opacity-95 backdrop-blur-md border rounded-lg p-4 min-w-80 shadow-glow-sm hover:shadow-glow transition-all duration-400 hover:scale-102 relative overflow-hidden ${conversion.status === 'completed'
              ? 'border-green-500 bg-green-500 bg-opacity-10 hover:border-green-400'
              : 'border-red-500 bg-red-500 bg-opacity-10 hover:border-red-400'
            }`}
        >
          {/* Background shimmer effect */}
          <div className={`absolute inset-0 bg-gradient-to-r from-transparent to-transparent animate-shimmer opacity-30 ${conversion.status === 'completed'
              ? 'via-green-500 via-opacity-5'
              : 'via-red-500 via-opacity-5'
            }`}></div>

          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {conversion.status === 'completed' ? (
                  <CheckCircle className="w-4 h-4 text-green-400" />
                ) : (
                  <XCircle className="w-4 h-4 text-red-400" />
                )}
                <span className="text-white font-medium text-sm truncate max-w-48" title={conversion.fileName}>
                  {conversion.fileName}
                </span>
              </div>
              <button
                onClick={onShowDetails}
                className="text-gray-400 hover:text-white transition-all duration-300 hover:scale-110 group"
                title="查看详细信息"
              >
                <Eye className="w-4 h-4 group-hover:scale-110 transition-transform duration-300" />
              </button>
            </div>

            <div className="mt-2">
              {conversion.status === 'completed' ? (
                <span className="text-green-400 text-xs">✓ 转换完成</span>
              ) : (
                <div>
                  <span className="text-red-400 text-xs">✗ 转换失败</span>
                  {conversion.error && (
                    <p className="text-red-300 text-xs mt-1 truncate" title={conversion.error}>
                      {conversion.error}
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      ))}

      {/* Summary Button (when multiple conversions) */}
      {totalConversions > 1 && (
        <button
          onClick={onShowDetails}
          className="w-full bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 rounded-lg p-3 text-white text-sm transition-all duration-300 hover:scale-102 hover:shadow-glow-sm group relative overflow-hidden"
        >
          {/* Button shimmer effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white via-opacity-5 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer transition-opacity duration-300"></div>

          <span className="relative z-10 flex items-center justify-center space-x-2">
            <Eye className="w-4 h-4 group-hover:scale-110 transition-transform duration-300" />
            <span>查看所有转换进度 ({totalConversions})</span>
          </span>
        </button>
      )}
    </div>
  )
}

export default ProgressNotification