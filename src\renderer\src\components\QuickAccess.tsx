import React from 'react'
import { <PERSON>, Star, TrendingUp } from 'lucide-react'
import type { FileType } from '../../../shared/types'

interface QuickAccessProps {
  onConversionClick: (from: string, to: string, fileType: FileType) => void
}

const QuickAccess: React.FC<QuickAccessProps> = ({ onConversionClick }) => {
  const popularConversions = [
    { from: 'mp4', to: 'avi', type: 'VIDEO' as FileType, label: 'MP4 → AVI', count: '1.2k' },
    { from: 'png', to: 'jpg', type: 'IMAGE' as FileType, label: 'PNG → JPG', count: '980' },
    { from: 'flac', to: 'mp3', type: 'AUDIO' as FileType, label: 'FLAC → MP3', count: '756' },
    { from: 'mov', to: 'mp4', type: 'VIDEO' as FileType, label: 'MOV → MP4', count: '654' }
  ]

  const recentConversions = [
    { from: 'jpg', to: 'webp', type: 'IMAGE' as FileType, label: 'JPG → WebP', time: '2分钟前' },
    { from: 'wav', to: 'mp3', type: 'AUDIO' as FileType, label: 'WAV → MP3', time: '15分钟前' },
    { from: 'mkv', to: 'mp4', type: 'VIDEO' as FileType, label: 'MKV → MP4', time: '1小时前' }
  ]

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'VIDEO':
        return 'text-red-400'
      case 'AUDIO':
        return 'text-green-400'
      case 'IMAGE':
        return 'text-blue-400'
      default:
        return 'text-gray-400'
    }
  }

  return (
    <div className="mb-16">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Popular Conversions */}
        <div className="bg-white bg-opacity-5 backdrop-blur-sm rounded-2xl p-6">
          <div className="flex items-center mb-6">
            <TrendingUp className="w-6 h-6 text-orange-400 mr-3" />
            <h3 className="text-xl font-semibold text-white">热门转换</h3>
          </div>
          <div className="space-y-3">
            {popularConversions.map((conversion, index) => (
              <div
                key={index}
                onClick={() => onConversionClick(conversion.from, conversion.to, conversion.type)}
                className="flex items-center justify-between p-3 rounded-lg bg-white bg-opacity-5 hover:bg-opacity-10 cursor-pointer transition-all duration-200 group"
              >
                <div className="flex items-center">
                  <Star className="w-4 h-4 text-yellow-400 mr-3" />
                  <span className={`font-medium ${getTypeColor(conversion.type)}`}>
                    {conversion.label}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-gray-400 text-sm">{conversion.count} 次使用</span>
                  <div className="w-2 h-2 rounded-full bg-green-400 opacity-0 group-hover:opacity-100 transition-opacity" />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Conversions */}
        <div className="bg-white bg-opacity-5 backdrop-blur-sm rounded-2xl p-6">
          <div className="flex items-center mb-6">
            <Clock className="w-6 h-6 text-blue-400 mr-3" />
            <h3 className="text-xl font-semibold text-white">最近使用</h3>
          </div>
          <div className="space-y-3">
            {recentConversions.map((conversion, index) => (
              <div
                key={index}
                onClick={() => onConversionClick(conversion.from, conversion.to, conversion.type)}
                className="flex items-center justify-between p-3 rounded-lg bg-white bg-opacity-5 hover:bg-opacity-10 cursor-pointer transition-all duration-200 group"
              >
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-gradient-to-r from-blue-400 to-purple-400 mr-3" />
                  <span className={`font-medium ${getTypeColor(conversion.type)}`}>
                    {conversion.label}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-gray-400 text-sm">{conversion.time}</span>
                  <div className="w-2 h-2 rounded-full bg-blue-400 opacity-0 group-hover:opacity-100 transition-opacity" />
                </div>
              </div>
            ))}
          </div>
          
          {recentConversions.length === 0 && (
            <div className="text-center py-8">
              <Clock className="w-12 h-12 text-gray-600 mx-auto mb-3" />
              <p className="text-gray-400">暂无最近转换记录</p>
              <p className="text-gray-500 text-sm mt-1">开始您的第一次转换吧！</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default QuickAccess