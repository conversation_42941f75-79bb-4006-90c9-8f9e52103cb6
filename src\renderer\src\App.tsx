import React, { useState, useEffect } from 'react'
import CustomTitleBar from './components/CustomTitleBar'
import VersionInfo from './components/VersionInfo'
import HomePage from './pages/HomePage'
import SettingsPage from './pages/SettingsPage'
import ErrorBoundary from './components/ErrorBoundary'
import ErrorNotificationContainer from './components/ErrorNotificationContainer'
import ErrorDialog from './components/ErrorDialog'
import { ErrorManager, ErrorInfo } from './utils/errorManager'
import { ThemeProvider, useTheme } from './contexts/ThemeContext'

function AppContent(): React.JSX.Element {
  const [currentPage, setCurrentPage] = useState<'home' | 'settings'>('home')
  const [currentError, setCurrentError] = useState<ErrorInfo | null>(null)
  const [showErrorDialog, setShowErrorDialog] = useState(false)
  const { theme, isLoading } = useTheme()

  useEffect(() => {
    // 初始化错误管理器
    const errorManager = ErrorManager.getInstance()

    // 监听显示错误详情的事件
    const handleShowErrorDetails = (event: CustomEvent<ErrorInfo>) => {
      setCurrentError(event.detail)
      setShowErrorDialog(true)
    }

    window.addEventListener('show-error-details', handleShowErrorDetails as EventListener)

    return () => {
      window.removeEventListener('show-error-details', handleShowErrorDetails as EventListener)
    }
  }, [])

  const handleSettingsClick = () => {
    setCurrentPage('settings')
  }

  const handleBackToHome = () => {
    setCurrentPage('home')
  }

  const handleCloseErrorDialog = () => {
    setShowErrorDialog(false)
    setCurrentError(null)
  }

  const handleRetryError = () => {
    // 这里可以实现重试逻辑
    // 具体的重试行为取决于错误类型
    setShowErrorDialog(false)
    setCurrentError(null)
  }

  // Show loading screen while theme is being loaded
  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-glow">
            <span className="text-2xl">🎨</span>
          </div>
          <div className="text-white text-xl font-medium">加载主题中...</div>
          <div className="mt-4 w-32 h-1 bg-gray-700 rounded-full mx-auto overflow-hidden">
            <div className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-shimmer"></div>
          </div>
        </div>
      </div>
    )
  }

  // Theme-specific background classes
  const backgroundClasses = theme === 'dark' 
    ? 'bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900'
    : 'bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100'

  const backgroundElementClasses = theme === 'dark'
    ? 'opacity-5'
    : 'opacity-10'

  return (
    <ErrorBoundary>
      <div className={`h-screen flex flex-col ${backgroundClasses} relative overflow-hidden transition-all duration-500`}>
        {/* Static background elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className={`absolute -top-40 -right-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl ${backgroundElementClasses} transition-opacity duration-500`}></div>
          <div className={`absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl ${backgroundElementClasses} transition-opacity duration-500`}></div>
        </div>

        <CustomTitleBar />
        
        <div className="flex-1 overflow-auto relative z-10">
          <div className="transition-all duration-300">
            {currentPage === 'home' && <HomePage />}
            {currentPage === 'settings' && <SettingsPage onBack={handleBackToHome} />}
          </div>
        </div>
        
        <VersionInfo />
        
        {/* 错误通知容器 */}
        <ErrorNotificationContainer />
        
        {/* 错误详情对话框 */}
        <ErrorDialog
          error={currentError}
          isVisible={showErrorDialog}
          onClose={handleCloseErrorDialog}
          onRetry={handleRetryError}
        />
      </div>
    </ErrorBoundary>
  )
}

function App(): React.JSX.Element {
  return (
    <ThemeProvider>
      <AppContent />
    </ThemeProvider>
  )
}

export default App
