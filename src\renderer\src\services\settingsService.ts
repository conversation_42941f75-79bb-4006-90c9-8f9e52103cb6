import { IPCService } from './ipcService'
import type { AppSettings } from '../../../shared/types'

export class SettingsService {
  static async getSettings(): Promise<AppSettings> {
    return IPCService.getSettings()
  }

  static async updateSettings(settings: Partial<AppSettings>): Promise<void> {
    return IPCService.updateSettings(settings)
  }

  static async selectOutputFolder(): Promise<Electron.OpenDialogReturnValue> {
    return IPCService.showOpenDialog({
      title: '选择输出文件夹',
      properties: ['openDirectory'],
      buttonLabel: '选择文件夹'
    })
  }

  static async checkFFmpegStatus(): Promise<boolean> {
    try {
      await IPCService.getSupportedFormats()
      return true
    } catch (error) {
      return false
    }
  }

  static async getAppVersion() {
    return IPCService.getAppVersion()
  }
}