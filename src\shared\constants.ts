// src/shared/constants.ts
export const IPC_CHANNELS = {
  // File conversion
  CONVERT_FILE: 'convert-file',
  GET_CONVERSION_PROGRESS: 'get-conversion-progress',
  CANCEL_CONVERSION: 'cancel-conversion',
  GET_SUPPORTED_FORMATS: 'get-supported-formats',

  // File system
  OPEN_OUTPUT_FOLDER: 'open-output-folder',
  OPEN_DIRECTORY: 'open-directory',
  SHOW_OPEN_DIALOG: 'show-open-dialog',
  SHOW_SAVE_DIALOG: 'show-save-dialog',

  // Settings
  GET_SETTINGS: 'get-settings',
  UPDATE_SETTINGS: 'update-settings',

  // App info
  GET_APP_VERSION: 'get-app-version',

  // Window controls
  WINDOW_MINIMIZE: 'window-minimize',
  WINDOW_MAXIMIZE: 'window-maximize',
  WINDOW_CLOSE: 'window-close',
  WINDOW_UNMAXIMIZE: 'window-unmaximize',

  // System
  GET_SYSTEM_INFO: 'get-system-info',
  SHOW_MESSAGE_BOX: 'show-message-box',
  SHOW_ERROR_BOX: 'show-error-box',

  // Image specific
  GET_IMAGE_INFO: 'get-image-info',
  OPTIMIZE_IMAGE: 'optimize-image',
  RESIZE_IMAGE: 'resize-image'
} as const

export const FILE_TYPES = {
  VIDEO: {
    INPUT: ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm'],
    OUTPUT: ['mp4', 'avi', 'mov', 'flv', 'webm'] // Removed MKV and WMV due to FFmpeg compatibility issues
  },
  AUDIO: {
    INPUT: ['mp3', 'wav', 'flac', 'ogg', 'wma'],
    OUTPUT: ['mp3', 'wav', 'flac', 'ogg'] // Removed AAC and M4A due to FFmpeg compatibility issues
  },
  IMAGE: {
    INPUT: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff', 'heic', 'heif'],
    OUTPUT: ['heic', 'jpg', 'jpeg', 'png', 'gif', 'webp', 'tiff', 'bmp']
  }
} as const

export const DEFAULT_PATHS = {
  OUTPUT_FOLDER: '格式转换',
  TEMP_FOLDER: '.format-converter-temp'
} as const

export const CONVERSION_STATUS = {
  IDLE: 'idle',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  ERROR: 'error',
  CANCELLED: 'cancelled'
} as const

// 用户限制配置
export const USER_CONFIG = {
  IS_VIP: false, // 改为 true 可以无限制使用
} as const
