// src/shared/types.ts
import { CONVERSION_STATUS, FILE_TYPES } from './constants'

export type ConversionStatus = typeof CONVERSION_STATUS[keyof typeof CONVERSION_STATUS]

export type FileType = keyof typeof FILE_TYPES

export type SupportedFormat = 
  | typeof FILE_TYPES.VIDEO.INPUT[number]
  | typeof FILE_TYPES.AUDIO.INPUT[number] 
  | typeof FILE_TYPES.IMAGE.INPUT[number]

export interface ConversionOptions {
  // Video options
  resolution?: string
  bitrate?: string
  framerate?: number
  
  // Audio options
  audioBitrate?: string
  sampleRate?: number
  channels?: number
  
  // Image options
  width?: number
  height?: number
  quality?: number
  compressionLevel?: number
}

export interface ConversionTask {
  id: string
  inputPath: string
  outputPath: string
  inputFormat: SupportedFormat
  outputFormat: SupportedFormat
  fileType: FileType
  options?: ConversionOptions
  status: ConversionStatus
  progress?: number
  error?: string
  startTime?: Date
  endTime?: Date
}

export interface AppInfo {
  version: string
  name: string
}

export interface AppSettings {
  outputPath: string
  theme: 'light' | 'dark'
  autoOpenOutput: boolean
  maxConcurrentTasks: number
}

export interface SystemInfo {
  platform: string
  arch: string
  release: string
  totalMemory: number
  freeMemory: number
  cpus: number
  homedir: string
  tmpdir: string
}

export interface DialogOptions {
  title?: string
  defaultPath?: string
  buttonLabel?: string
  filters?: Array<{
    name: string
    extensions: string[]
  }>
  properties?: Array<'openFile' | 'openDirectory' | 'multiSelections' | 'showHiddenFiles'>
}

export interface MessageBoxOptions {
  type?: 'none' | 'info' | 'error' | 'question' | 'warning'
  buttons?: string[]
  defaultId?: number
  title?: string
  message: string
  detail?: string
  checkboxLabel?: string
  checkboxChecked?: boolean
  icon?: string
  cancelId?: number
  noLink?: boolean
  normalizeAccessKeys?: boolean
}

// Trial system types
export type TrialCategory = 'VIDEO' | 'AUDIO' | 'IMAGE'

export interface TrialStatus {
  VIDEO: boolean  // true if trial used
  AUDIO: boolean  // true if trial used
  IMAGE: boolean  // true if trial used
}

export interface UserTrialState {
  isVip: boolean
  trialStatus: TrialStatus
  lastUpdated: string
}