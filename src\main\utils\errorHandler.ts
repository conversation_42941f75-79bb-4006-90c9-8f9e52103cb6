import { BrowserWindow } from 'electron'
import * as fs from 'fs'
import * as path from 'path'
import * as os from 'os'
import { ConversionError, ErrorType, ErrorSeverity } from '../../shared/errors'

export interface ErrorLog {
  id: string
  error: ConversionError
  context?: any
  userAgent?: string
  timestamp: Date
}

export class ErrorHandler {
  private static instance: ErrorHandler
  private mainWindow: BrowserWindow | null = null
  private errorLogs: ErrorLog[] = []
  private logFilePath: string
  private maxLogEntries = 1000

  private constructor() {
    // 创建日志文件路径
    const logDir = path.join(os.homedir(), '.format-converter', 'logs')
    this.ensureDirectoryExists(logDir)
    this.logFilePath = path.join(logDir, 'error.log')
    
    // 加载现有日志
    this.loadErrorLogs()
  }

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler()
    }
    return ErrorHandler.instance
  }

  setMainWindow(window: <PERSON><PERSON>erWindow): void {
    this.mainWindow = window
  }

  // 处理错误的主要方法
  async handleError(error: Error | ConversionError, context?: any): Promise<void> {
    let conversionError: ConversionError

    // 转换为 ConversionError
    if (error instanceof ConversionError) {
      conversionError = error
    } else {
      conversionError = this.categorizeError(error)
    }

    // 创建错误日志
    const errorLog: ErrorLog = {
      id: this.generateErrorId(),
      error: conversionError,
      context,
      timestamp: new Date()
    }

    // 记录错误
    this.logError(errorLog)

    // 根据错误严重程度决定处理方式
    await this.processError(errorLog)
  }

  // 根据原生错误分类
  private categorizeError(error: Error): ConversionError {
    const message = error.message.toLowerCase()

    // 文件相关错误
    if (message.includes('enoent') || message.includes('file not found')) {
      return new ConversionError(ErrorType.FILE_NOT_FOUND, error.message, {
        severity: ErrorSeverity.MEDIUM,
        cause: error
      })
    }

    if (message.includes('eacces') || message.includes('permission denied')) {
      return new ConversionError(ErrorType.FILE_ACCESS_DENIED, error.message, {
        severity: ErrorSeverity.MEDIUM,
        cause: error
      })
    }

    if (message.includes('enospc') || message.includes('no space left')) {
      return new ConversionError(ErrorType.INSUFFICIENT_DISK_SPACE, error.message, {
        severity: ErrorSeverity.HIGH,
        cause: error
      })
    }

    // FFmpeg 相关错误
    if (message.includes('ffmpeg') && (message.includes('not found') || message.includes('command not found'))) {
      return new ConversionError(ErrorType.FFMPEG_NOT_FOUND, error.message, {
        severity: ErrorSeverity.CRITICAL,
        cause: error,
        recoverable: false
      })
    }

    // Sharp 相关错误
    if (message.includes('sharp') && message.includes('not available')) {
      return new ConversionError(ErrorType.SHARP_NOT_AVAILABLE, error.message, {
        severity: ErrorSeverity.CRITICAL,
        cause: error,
        recoverable: false
      })
    }

    // 转换失败
    if (message.includes('conversion failed') || message.includes('encode') || message.includes('decode')) {
      return new ConversionError(ErrorType.CONVERSION_FAILED, error.message, {
        severity: ErrorSeverity.HIGH,
        cause: error
      })
    }

    // 默认为未知错误
    return new ConversionError(ErrorType.UNKNOWN_ERROR, error.message, {
      severity: ErrorSeverity.MEDIUM,
      cause: error
    })
  }

  // 处理错误
  private async processError(errorLog: ErrorLog): Promise<void> {
    const { error } = errorLog

    // 发送错误到渲染进程
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('error-occurred', {
        id: errorLog.id,
        type: error.type,
        message: error.message,
        severity: error.severity,
        recoverable: error.recoverable,
        timestamp: errorLog.timestamp.toISOString(),
        details: error.details
      })
    }

    // 根据严重程度处理
    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        await this.handleCriticalError(errorLog)
        break
      case ErrorSeverity.HIGH:
        await this.handleHighSeverityError(errorLog)
        break
      case ErrorSeverity.MEDIUM:
        await this.handleMediumSeverityError(errorLog)
        break
      case ErrorSeverity.LOW:
        await this.handleLowSeverityError(errorLog)
        break
    }
  }

  // 处理严重错误
  private async handleCriticalError(errorLog: ErrorLog): Promise<void> {
    console.error('CRITICAL ERROR:', errorLog.error)
    
    // 立即保存日志
    await this.saveErrorLogs()
    
    // 可能需要显示错误对话框或重启应用
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('critical-error', {
        error: errorLog.error.toJSON(),
        requiresRestart: !errorLog.error.recoverable
      })
    }
  }

  // 处理高严重性错误
  private async handleHighSeverityError(errorLog: ErrorLog): Promise<void> {
    console.error('HIGH SEVERITY ERROR:', errorLog.error)
    
    // 发送详细错误信息到渲染进程
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('high-severity-error', {
        error: errorLog.error.toJSON()
      })
    }
  }

  // 处理中等严重性错误
  private async handleMediumSeverityError(errorLog: ErrorLog): Promise<void> {
    console.warn('MEDIUM SEVERITY ERROR:', errorLog.error)
  }

  // 处理低严重性错误
  private async handleLowSeverityError(errorLog: ErrorLog): Promise<void> {
    console.info('LOW SEVERITY ERROR:', errorLog.error)
  }

  // 记录错误
  private logError(errorLog: ErrorLog): void {
    this.errorLogs.push(errorLog)
    
    // 限制日志数量
    if (this.errorLogs.length > this.maxLogEntries) {
      this.errorLogs = this.errorLogs.slice(-this.maxLogEntries)
    }
    
    // 异步保存日志
    this.saveErrorLogs().catch(err => {
      console.error('Failed to save error logs:', err)
    })
  }

  // 保存错误日志到文件
  private async saveErrorLogs(): Promise<void> {
    try {
      const logData = this.errorLogs.map(log => ({
        id: log.id,
        timestamp: log.timestamp.toISOString(),
        type: log.error.type,
        severity: log.error.severity,
        message: log.error.message,
        details: log.error.details,
        context: log.context,
        stack: log.error.stack
      }))

      await fs.promises.writeFile(
        this.logFilePath,
        JSON.stringify(logData, null, 2),
        'utf8'
      )
    } catch (error) {
      console.error('Failed to save error logs:', error)
    }
  }

  // 加载错误日志
  private loadErrorLogs(): void {
    try {
      if (fs.existsSync(this.logFilePath)) {
        const logData = fs.readFileSync(this.logFilePath, 'utf8')
        const logs = JSON.parse(logData)
        
        this.errorLogs = logs.map((log: any) => ({
          id: log.id,
          timestamp: new Date(log.timestamp),
          error: new ConversionError(log.type, log.message, {
            severity: log.severity,
            details: log.details
          }),
          context: log.context
        }))
      }
    } catch (error) {
      console.error('Failed to load error logs:', error)
      this.errorLogs = []
    }
  }

  // 获取错误统计
  getErrorStats(): {
    total: number
    bySeverity: Record<ErrorSeverity, number>
    byType: Record<ErrorType, number>
    recent: ErrorLog[]
  } {
    const bySeverity = {
      [ErrorSeverity.LOW]: 0,
      [ErrorSeverity.MEDIUM]: 0,
      [ErrorSeverity.HIGH]: 0,
      [ErrorSeverity.CRITICAL]: 0
    }

    const byType: Record<ErrorType, number> = {} as any

    this.errorLogs.forEach(log => {
      bySeverity[log.error.severity]++
      byType[log.error.type] = (byType[log.error.type] || 0) + 1
    })

    // 获取最近的错误（最近24小时）
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
    const recent = this.errorLogs.filter(log => log.timestamp > oneDayAgo)

    return {
      total: this.errorLogs.length,
      bySeverity,
      byType,
      recent
    }
  }

  // 清除旧日志
  clearOldLogs(daysToKeep: number = 30): void {
    const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000)
    this.errorLogs = this.errorLogs.filter(log => log.timestamp > cutoffDate)
    this.saveErrorLogs()
  }

  // 生成错误ID
  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 确保目录存在
  private ensureDirectoryExists(dirPath: string): void {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true })
    }
  }

  // 导出错误日志
  async exportErrorLogs(outputPath: string): Promise<void> {
    try {
      const exportData = {
        exportDate: new Date().toISOString(),
        totalErrors: this.errorLogs.length,
        errors: this.errorLogs.map(log => ({
          id: log.id,
          timestamp: log.timestamp.toISOString(),
          type: log.error.type,
          severity: log.error.severity,
          message: log.error.message,
          details: log.error.details,
          context: log.context,
          recoverable: log.error.recoverable
        }))
      }

      await fs.promises.writeFile(
        outputPath,
        JSON.stringify(exportData, null, 2),
        'utf8'
      )
    } catch (error) {
      throw new ConversionError(ErrorType.FILE_ACCESS_DENIED, '无法导出错误日志', {
        cause: error as Error
      })
    }
  }
}