import ffmpeg from 'fluent-ffmpeg'
import * as path from 'path'
import { PathUtils } from '../utils/pathUtils'
import { FFmpegManager } from '../utils/ffmpegManager'
import { ProgressManager } from '../utils/progressManager'
import { ErrorHandler } from '../utils/errorHandler'
import { ConversionError, ErrorFactory, ErrorType } from '../../shared/errors'
import type { ConversionOptions } from '../../shared/types'

export class AudioConverter {
  private static initialized = false

  static async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      const { ffmpegPath, ffprobePath } = await FFmpegManager.initialize()
      
      if (ffmpegPath && ffprobePath) {
        ffmpeg.setFfmpegPath(ffmpegPath)
        ffmpeg.setFfprobePath(ffprobePath)
        this.initialized = true
        console.log('AudioConverter initialized with FFmpeg')
      } else {
        const error = ErrorFactory.ffmpegNotFound()
        await ErrorHandler.getInstance().handleError(error)
        throw error
      }
    } catch (error) {
      console.error('Failed to initialize AudioConverter:', error)
      if (!(error instanceof ConversionError)) {
        const conversionError = ErrorFactory.fromNativeError(error as Error, ErrorType.INITIALIZATION_FAILED)
        await ErrorHandler.getInstance().handleError(conversionError)
        throw conversionError
      }
      throw error
    }
  }

  static async convert(
    inputPath: string,
    outputPath: string,
    _inputFormat: string,
    outputFormat: string,
    options?: ConversionOptions,
    progressId?: string
  ): Promise<void> {
    const errorHandler = ErrorHandler.getInstance()

    // 检查初始化状态
    if (!this.initialized || !FFmpegManager.isAvailable()) {
      const error = ErrorFactory.ffmpegNotFound()
      await errorHandler.handleError(error, { inputPath, outputPath, outputFormat })
      throw error
    }

    // 验证输入文件
    if (!PathUtils.isValidPath(inputPath)) {
      const error = ErrorFactory.fileNotFound(inputPath)
      await errorHandler.handleError(error, { outputPath, outputFormat })
      throw error
    }

    // 验证输出目录
    try {
      const outputDir = path.dirname(outputPath)
      PathUtils.ensureDirectoryExists(outputDir)
    } catch (err) {
      const error = ErrorFactory.fileAccessDenied(path.dirname(outputPath))
      await errorHandler.handleError(error, { inputPath, outputPath })
      throw error
    }

    const progressManager = ProgressManager.getInstance()

    return new Promise((resolve, reject) => {
      let command = ffmpeg(inputPath)

      try {
        // Remove video stream for audio-only conversion
        command = command.noVideo()

        // Apply audio options with validation
        if (options?.audioBitrate) {
          if (!/^\d+k?$/i.test(options.audioBitrate)) {
            const error = new ConversionError(
              ErrorType.INVALID_CONVERSION_OPTIONS,
              `Invalid audio bitrate format: ${options.audioBitrate}`,
              { details: { audioBitrate: options.audioBitrate } }
            )
            errorHandler.handleError(error, { inputPath, outputPath, options })
            reject(error)
            return
          }
          command = command.audioBitrate(options.audioBitrate)
        }

        if (options?.sampleRate) {
          if (typeof options.sampleRate === 'number' && options.sampleRate > 0) {
            command = command.audioFrequency(options.sampleRate)
          }
        }

        if (options?.channels) {
          if (typeof options.channels === 'number' && options.channels > 0 && options.channels <= 8) {
            command = command.audioChannels(options.channels)
          }
        }

        // Set audio codec based on output format
        const audioCodec = this.getAudioCodec(outputFormat)
        if (audioCodec) {
          command = command.audioCodec(audioCodec)
        } else {
          const error = ErrorFactory.unsupportedFormat('audio', outputFormat)
          errorHandler.handleError(error, { inputPath, outputPath, outputFormat })
          reject(error)
          return
        }

        // Set output format
        command = command.format(outputFormat)

        // Handle progress
        command.on('progress', (progress) => {
          if (progressId) {
            const progressPercent = Math.round(progress.percent || 0)
            
            // Extract additional progress information
            const currentTime = progress.timemark || '00:00:00'
            const bitrate = progress.currentKbps ? `${Math.round(progress.currentKbps)}kbps` : undefined
            const size = progress.targetSize ? ProgressManager.formatFileSize(progress.targetSize * 1024) : undefined

            progressManager.updateProgress(progressId, {
              progress: progressPercent,
              currentTime,
              bitrate,
              size,
              eta: progress.percent ? ProgressManager.formatTime((100 - progress.percent) * (Date.now() - (progressManager.getConversion(progressId)?.startTime || Date.now())) / (progress.percent * 1000)) : undefined
            })
          }
        })

        // Handle completion
        command.on('end', () => {
          console.log(`Audio conversion completed: ${inputPath} -> ${outputPath}`)
          if (progressId) {
            progressManager.completeConversion(progressId)
          }
          resolve()
        })

        // Handle errors with detailed error processing
        command.on('error', async (err) => {
          console.error('Audio conversion error:', err)
          
          // Create detailed conversion error
          const conversionError = ErrorFactory.conversionFailed(err.message, {
            inputPath,
            outputPath,
            outputFormat,
            options,
            ffmpegError: err
          })

          // Handle the error
          await errorHandler.handleError(conversionError, {
            converter: 'AudioConverter',
            inputPath,
            outputPath,
            options
          })

          if (progressId) {
            progressManager.errorConversion(progressId, conversionError.message)
          }

          reject(conversionError)
        })

        // Start conversion
        if (progressId) {
          progressManager.startConversion(progressId)
        }
        command.save(outputPath)

      } catch (setupError) {
        // Handle setup errors
        const error = ErrorFactory.fromNativeError(setupError as Error, ErrorType.INVALID_CONVERSION_OPTIONS)
        errorHandler.handleError(error, { inputPath, outputPath, options })
        reject(error)
      }
    })
  }

  private static getAudioCodec(format: string): string | null {
    const codecMap: { [key: string]: string } = {
      'mp3': 'libmp3lame',
      'aac': 'aac',
      'ogg': 'libvorbis',
      'flac': 'flac',
      'wav': 'pcm_s16le',
      'm4a': 'aac'
    }
    return codecMap[format.toLowerCase()] || null
  }

  static getSupportedFormats(): { input: string[], output: string[] } {
    return {
      input: ['mp3', 'wav', 'flac', 'ogg', 'wma', 'opus', 'ac3'],
      output: ['mp3', 'wav', 'flac', 'ogg', 'opus'] // Removed AAC and M4A due to FFmpeg compatibility issues
    }
  }

  static async getAudioInfo(inputPath: string): Promise<any> {
    if (!this.initialized || !FFmpegManager.isAvailable()) {
      throw new Error('FFmpeg not available')
    }

    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(inputPath, (err, metadata) => {
        if (err) {
          reject(new Error(`Failed to get audio info: ${err.message}`))
        } else {
          resolve(metadata)
        }
      })
    })
  }
}