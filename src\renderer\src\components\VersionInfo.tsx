import React, { useEffect, useState } from 'react'
import { IPCService } from '../services/ipcService'
import { useTheme } from '../contexts/ThemeContext'
import type { AppInfo } from '../../../shared/types'

const VersionInfo: React.FC = () => {
  const { theme } = useTheme()
  const [appInfo, setAppInfo] = useState<AppInfo>({ version: '', name: '' })

  useEffect(() => {
    IPCService.getAppVersion().then(setAppInfo)
  }, [])

  // Theme-specific classes
  const backgroundClasses = theme === 'dark' 
    ? 'bg-black bg-opacity-30 border-white border-opacity-10 hover:border-opacity-20'
    : 'bg-white bg-opacity-70 border-gray-300 border-opacity-50 hover:border-opacity-80'
  
  const textClasses = theme === 'dark' 
    ? 'text-gray-400 group-hover:text-gray-300'
    : 'text-gray-600 group-hover:text-gray-800'

  return (
    <div className={`fixed bottom-4 right-4 text-xs ${textClasses} ${backgroundClasses} px-3 py-2 rounded-lg backdrop-blur-md border transition-all duration-300 hover:scale-105 group`}>
      <div className="flex items-center space-x-2">
        <div className="w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full"></div>
        <span className="font-medium transition-colors duration-300">
          v{appInfo.version}
        </span>
      </div>
    </div>
  )
}

export default VersionInfo