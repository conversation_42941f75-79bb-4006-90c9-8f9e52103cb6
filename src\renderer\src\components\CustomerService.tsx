import React, { useState } from 'react'
import { Copy, X, Check } from 'lucide-react'
import WxQrCode from '@renderer/assets/wx.png';
import { UserIcon } from './UserIcon';

interface CustomerServiceProps {
  theme?: 'light' | 'dark'
}

const MessageIcon: React.FC<{ className?: string }> = ({ className = '' }) => (
  <svg className={className} viewBox="0 0 1110 1024" strokeWidth="2" width="20" height="20">
    <path d="M42.666667 532.672l0-42.666667 1024 0 0 42.666667L42.666667 532.672zM1002.666667 106.005333c0-23.552-19.093333-42.666667-42.666667-42.666667L725.333333 63.338667 725.333333 20.672l234.666667 0c47.125333 0 85.333333 38.208 85.333333 85.333333l0 256-42.666667 0L1002.666667 106.005333zM106.666667 106.005333l0 256L64 362.005333 64 106.005333c0-47.125333 38.208-85.333333 85.333333-85.333333l234.666667 0 0 42.666667L149.333333 63.338667C125.76 63.338667 106.666667 82.432 106.666667 106.005333zM106.666667 916.672c0 23.573333 19.093333 42.666667 42.666667 42.666667l234.666667 0 0 43.136L149.333333 1002.005333c-47.125333 0-85.333333-38.208-85.333333-85.333333L64 660.672l42.666667 0L106.666667 916.672zM1002.666667 916.672 1002.666667 660.672l42.666667 0 0 256c0 47.125333-35.797333 86.933333-82.922667 86.933333L725.333333 1003.136 725.333333 959.338667l234.666667 0C983.573333 959.338667 1002.666667 940.245333 1002.666667 916.672z" p-id="6001" fill="#ffffff">
    </path>
  </svg>
)

const CustomerService: React.FC<CustomerServiceProps> = ({ theme = 'dark' }) => {
  const [showDropdown, setShowDropdown] = useState(false)
  const [showWechatModal, setShowWechatModal] = useState(false)
  const [showCopySuccess, setShowCopySuccess] = useState(false)

  const qqNumber = '3860382229' // QQ号码

  const handleQQClick = () => {
    setShowDropdown(false)
    // 直接复制QQ号
    navigator.clipboard.writeText(qqNumber).then(() => {
      setShowCopySuccess(true)
      // 3秒后隐藏提示
      setTimeout(() => {
        setShowCopySuccess(false)
      }, 3000)
    }).catch(err => {
      console.error('复制QQ号失败', err)
    })
  }

  const handleWechatClick = () => {
    setShowDropdown(false)
    setShowWechatModal(true)
  }

  // Theme-specific classes
  const buttonClasses = theme === 'dark'
    ? 'text-gray-300 hover:text-white hover:bg-gray-700 hover:bg-opacity-80'
    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-200 hover:bg-opacity-80'

  const dropdownClasses = theme === 'dark'
    ? 'bg-gray-800 border-gray-600 shadow-xl'
    : 'bg-white border-gray-300 shadow-xl'

  const textClasses = theme === 'dark' ? 'text-white' : 'text-gray-800'
  const subtextClasses = theme === 'dark' ? 'text-gray-300' : 'text-gray-600'

  return (
    <div className="relative">
      {/* 客服按钮 */}
      <button
        onClick={() => setShowDropdown(!showDropdown)}
        className={`window-control-btn gap-x-2 h-8 flex items-center justify-center ${buttonClasses} transition-all duration-300 hover:scale-110 group`}
        title="联系客服"
      >
        <UserIcon />
        <span className="text-xs">联系客服</span>
      </button>

      {/* 下拉菜单 */}
      {showDropdown && (
        <>
          {/* 背景遮罩 */}
          <div
            className="fixed inset-0 z-40"
            onClick={() => setShowDropdown(false)}
          />

          {/* 下拉内容 */}
          <div className={`absolute top-full right-0 mt-1 w-48 ${dropdownClasses} border rounded-lg shadow-glow-sm z-50 overflow-hidden`}>
            {/* QQ选项 */}
            <button
              onClick={handleQQClick}
              className={`w-full flex items-center px-4 py-3 ${theme === 'dark' ? 'hover:bg-gray-700' : 'hover:bg-gray-100'} transition-colors duration-200 group`}
            >
              <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-200">
                <span className="text-white text-xs font-bold">Q</span>
              </div>
              <div className="flex-1 text-left">
                <div className={`${textClasses} text-sm font-medium`}>客服QQ</div>
                <div className={`${subtextClasses} text-xs`}>{qqNumber}</div>
              </div>
              <Copy className={`w-4 h-4 ${subtextClasses} group-hover:text-blue-400 transition-colors duration-200`} />
            </button>

            {/* 微信选项 */}
            <button
              onClick={handleWechatClick}
              className={`w-full flex items-center px-4 py-3 ${theme === 'dark' ? 'hover:bg-gray-700' : 'hover:bg-gray-100'} transition-colors duration-200 group`}
            >
              <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-200">
                <span className="text-white text-xs font-bold">微</span>
              </div>
              <div className="flex-1 text-left">
                <div className={`${textClasses} text-sm font-medium`}>客服微信</div>
                <div className={`${subtextClasses} text-xs`}>点击扫码添加</div>
              </div>
              <MessageIcon className={`w-4 h-4 ${subtextClasses} group-hover:text-green-400 transition-colors duration-200`} />
            </button>
          </div>
        </>
      )}

      {/* 复制成功提示 */}
      {showCopySuccess && (
        <div className="fixed top-20 right-4 z-50">
          <div className="bg-green-500 bg-opacity-20 border border-green-500 rounded-lg p-4 max-w-sm backdrop-blur-md shadow-glow-sm">
            <div className="flex items-center space-x-3">
              <Check className="w-5 h-5 text-green-400 flex-shrink-0" />
              <div>
                <h4 className="text-white font-medium text-sm">QQ号已复制</h4>
                <p className="text-gray-300 text-xs mt-1">QQ号 {qqNumber} 已复制到剪贴板</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 微信二维码弹窗 */}
      {showWechatModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
          <div className={`${theme === 'dark' ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-300'} border rounded-xl shadow-glow max-w-md w-full mx-4 overflow-hidden`}>
            {/* 弹窗头部 */}
            <div className={`flex items-center justify-between p-4 border-b ${theme === 'dark' ? 'border-gray-600' : 'border-gray-300'}`}>
              <h3 className={`${textClasses} text-lg font-semibold`}>扫码添加微信客服</h3>
              <button
                onClick={() => setShowWechatModal(false)}
                className={`${buttonClasses} p-1 rounded-full transition-all duration-200 hover:scale-110`}
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* 弹窗内容 */}
            <div className="p-6 text-center">
              <div className={`mx-auto ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-200'} rounded-lg flex items-center justify-center mb-4`}>
                <img
                  src={WxQrCode}
                  alt="微信客服二维码"
                  className="object-contain"
                />
              </div>
            </div>

            {/* 弹窗底部 */}
            <div className={`p-4 border-t ${theme === 'dark' ? 'border-gray-600' : 'border-gray-300'} flex justify-center`}>
              <button
                onClick={() => setShowWechatModal(false)}
                className="px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors duration-200 hover:scale-105"
              >
                我知道了
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default CustomerService