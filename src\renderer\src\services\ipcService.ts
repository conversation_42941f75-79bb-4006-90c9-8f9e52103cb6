import { IPC_CHANNELS } from '../../../shared/constants'
import type {
  SystemInfo,
  DialogOptions,
  MessageBoxOptions,
  AppInfo,
  AppSettings
} from '../../../shared/types'

export class IPCService {
  // Window controls
  static async minimizeWindow(): Promise<void> {
    return window.electron.ipcRenderer.invoke(IPC_CHANNELS.WINDOW_MINIMIZE)
  }

  static async maximizeWindow(): Promise<void> {
    return window.electron.ipcRenderer.invoke(IPC_CHANNELS.WINDOW_MAXIMIZE)
  }

  static async closeWindow(): Promise<void> {
    return window.electron.ipcRenderer.invoke(IPC_CHANNELS.WINDOW_CLOSE)
  }

  // App info
  static async getAppVersion(): Promise<AppInfo> {
    return window.electron.ipcRenderer.invoke(IPC_CHANNELS.GET_APP_VERSION)
  }

  // System info
  static async getSystemInfo(): Promise<SystemInfo> {
    return window.electron.ipcRenderer.invoke(IPC_CHANNELS.GET_SYSTEM_INFO)
  }

  // Settings
  static async getSettings(): Promise<AppSettings> {
    return window.electron.ipcRenderer.invoke(IPC_CHANNELS.GET_SETTINGS)
  }

  static async updateSettings(settings: Partial<AppSettings>): Promise<void> {
    return window.electron.ipcRenderer.invoke(IPC_CHANNELS.UPDATE_SETTINGS, settings)
  }

  // File system
  static async openOutputFolder(path?: string): Promise<void> {
    return window.electron.ipcRenderer.invoke(IPC_CHANNELS.OPEN_OUTPUT_FOLDER, path)
  }

  static async openDirectory(outputPath: string): Promise<{ success: boolean; error?: string }> {
    return window.electron.ipcRenderer.invoke(IPC_CHANNELS.OPEN_DIRECTORY, outputPath)
  }

  static async showOpenDialog(options: DialogOptions): Promise<Electron.OpenDialogReturnValue> {
    return window.electron.ipcRenderer.invoke(IPC_CHANNELS.SHOW_OPEN_DIALOG, options)
  }

  static async showSaveDialog(options: DialogOptions): Promise<Electron.SaveDialogReturnValue> {
    return window.electron.ipcRenderer.invoke(IPC_CHANNELS.SHOW_SAVE_DIALOG, options)
  }

  // Dialogs
  static async showMessageBox(options: MessageBoxOptions): Promise<Electron.MessageBoxReturnValue> {
    return window.electron.ipcRenderer.invoke(IPC_CHANNELS.SHOW_MESSAGE_BOX, options)
  }

  static async showErrorBox(title: string, content: string): Promise<void> {
    return window.electron.ipcRenderer.invoke(IPC_CHANNELS.SHOW_ERROR_BOX, title, content)
  }

  // Conversion
  static async getSupportedFormats(): Promise<
    typeof import('../../../shared/constants').FILE_TYPES
  > {
    return window.electron.ipcRenderer.invoke(IPC_CHANNELS.GET_SUPPORTED_FORMATS)
  }

  // Image specific
  static async getImageInfo(imagePath: string): Promise<any> {
    return window.electron.ipcRenderer.invoke(IPC_CHANNELS.GET_IMAGE_INFO, imagePath)
  }

  static async optimizeImage(
    inputPath: string,
    outputPath: string,
    quality: number
  ): Promise<void> {
    return window.electron.ipcRenderer.invoke(
      IPC_CHANNELS.OPTIMIZE_IMAGE,
      inputPath,
      outputPath,
      quality
    )
  }

  static async resizeImage(
    inputPath: string,
    outputPath: string,
    width?: number,
    height?: number
  ): Promise<void> {
    return window.electron.ipcRenderer.invoke(
      IPC_CHANNELS.RESIZE_IMAGE,
      inputPath,
      outputPath,
      width,
      height
    )
  }

  // Progress management
  static async getAllConversions(): Promise<any[]> {
    return window.electron.ipcRenderer.invoke('get-all-conversions')
  }

  static async getActiveConversions(): Promise<any[]> {
    return window.electron.ipcRenderer.invoke('get-active-conversions')
  }

  static async clearCompletedConversions(): Promise<void> {
    return window.electron.ipcRenderer.invoke('clear-completed-conversions')
  }

  static async removeConversion(conversionId: string): Promise<void> {
    return window.electron.ipcRenderer.invoke('remove-conversion', conversionId)
  }

  // Utility methods
  static getPathForFile(file: File): string {
    return window.electron.webUtils.getPathForFile(file)
  }

  // Event listeners
  static onWindowEvent(event: string, callback: (...args: any[]) => void): () => void {
    window.electron.ipcRenderer.on(event, callback)
    return () => window.electron.ipcRenderer.removeListener(event, callback)
  }

  static removeAllListeners(event: string): void {
    window.electron.ipcRenderer.removeAllListeners(event)
  }
}
