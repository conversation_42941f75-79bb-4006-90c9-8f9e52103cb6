import React, { useState } from 'react'
import { X, Alert<PERSON>riangle, XCircle, AlertCircle, Info, ChevronDown, ChevronUp, <PERSON><PERSON>, Refresh<PERSON>w } from 'lucide-react'
import { ERROR_SOLUTIONS, USER_FRIENDLY_MESSAGES, ErrorType, ErrorSeverity } from '../../../shared/errors'

interface ErrorInfo {
  id: string
  type: ErrorType
  message: string
  severity: ErrorSeverity
  recoverable: boolean
  timestamp: string
  details?: any
}

interface ErrorDialogProps {
  error: ErrorInfo | null
  isVisible: boolean
  onClose: () => void
  onRetry?: () => void
}

const ErrorDialog: React.FC<ErrorDialogProps> = ({
  error,
  isVisible,
  onClose,
  onRetry
}) => {
  const [showDetails, setShowDetails] = useState(false)
  const [showTechnicalDetails, setShowTechnicalDetails] = useState(false)

  if (!isVisible || !error) return null

  const getSeverityIcon = () => {
    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        return <XCircle className="w-8 h-8 text-red-500" />
      case ErrorSeverity.HIGH:
        return <AlertTriangle className="w-8 h-8 text-orange-500" />
      case ErrorSeverity.MEDIUM:
        return <AlertCircle className="w-8 h-8 text-yellow-500" />
      case ErrorSeverity.LOW:
        return <Info className="w-8 h-8 text-blue-500" />
      default:
        return <AlertCircle className="w-8 h-8 text-gray-500" />
    }
  }

  const getSeverityColor = () => {
    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        return 'border-red-500 bg-red-50'
      case ErrorSeverity.HIGH:
        return 'border-orange-500 bg-orange-50'
      case ErrorSeverity.MEDIUM:
        return 'border-yellow-500 bg-yellow-50'
      case ErrorSeverity.LOW:
        return 'border-blue-500 bg-blue-50'
      default:
        return 'border-gray-500 bg-gray-50'
    }
  }

  const getSeverityText = () => {
    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        return '严重错误'
      case ErrorSeverity.HIGH:
        return '高级错误'
      case ErrorSeverity.MEDIUM:
        return '中级错误'
      case ErrorSeverity.LOW:
        return '轻微错误'
      default:
        return '未知错误'
    }
  }

  const getUserFriendlyMessage = () => {
    return USER_FRIENDLY_MESSAGES[error.type] || error.message
  }

  const getSolutions = () => {
    return ERROR_SOLUTIONS[error.type] || ['请稍后重试', '如果问题持续存在，请联系技术支持']
  }

  const copyErrorDetails = () => {
    const errorDetails = {
      id: error.id,
      type: error.type,
      severity: error.severity,
      message: error.message,
      timestamp: error.timestamp,
      details: error.details
    }
    
    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
      .then(() => {
        // 可以显示复制成功的提示
        console.log('Error details copied to clipboard')
      })
      .catch(err => {
        console.error('Failed to copy error details:', err)
      })
  }

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-hidden shadow-2xl">
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b-2 ${getSeverityColor()}`}>
          <div className="flex items-center space-x-4">
            {getSeverityIcon()}
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {getSeverityText()}
              </h2>
              <p className="text-sm text-gray-600">
                {new Date(error.timestamp).toLocaleString()}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {/* User-friendly message */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-2">发生了什么？</h3>
            <p className="text-gray-700 leading-relaxed">
              {getUserFriendlyMessage()}
            </p>
          </div>

          {/* Solutions */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">解决方案</h3>
            <div className="space-y-2">
              {getSolutions().map((solution, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5">
                    {index + 1}
                  </div>
                  <p className="text-gray-700">{solution}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Show more details toggle */}
          <div className="mb-4">
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 transition-colors"
            >
              {showDetails ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
              <span>{showDetails ? '隐藏详细信息' : '显示详细信息'}</span>
            </button>
          </div>

          {/* Detailed information */}
          {showDetails && (
            <div className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">错误类型</h4>
                <p className="text-gray-700 font-mono text-sm">{error.type}</p>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">错误ID</h4>
                <p className="text-gray-700 font-mono text-sm">{error.id}</p>
              </div>

              {error.details && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900">上下文信息</h4>
                    <button
                      onClick={() => setShowTechnicalDetails(!showTechnicalDetails)}
                      className="text-sm text-blue-600 hover:text-blue-700"
                    >
                      {showTechnicalDetails ? '隐藏' : '显示'}
                    </button>
                  </div>
                  {showTechnicalDetails && (
                    <pre className="text-xs text-gray-600 bg-white p-3 rounded border overflow-x-auto">
                      {JSON.stringify(error.details, null, 2)}
                    </pre>
                  )}
                </div>
              )}

              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">原始错误消息</h4>
                <p className="text-gray-700 text-sm font-mono bg-white p-3 rounded border">
                  {error.message}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 bg-gray-50 border-t">
          <div className="flex items-center space-x-3">
            <button
              onClick={copyErrorDetails}
              className="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
              title="复制错误详情"
            >
              <Copy className="w-4 h-4" />
              <span className="text-sm">复制详情</span>
            </button>
            
            {error.recoverable && (
              <span className="text-sm text-green-600 bg-green-100 px-2 py-1 rounded">
                可恢复
              </span>
            )}
          </div>

          <div className="flex items-center space-x-3">
            {error.recoverable && onRetry && (
              <button
                onClick={onRetry}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <RefreshCw className="w-4 h-4" />
                <span>重试</span>
              </button>
            )}
            
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              关闭
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ErrorDialog