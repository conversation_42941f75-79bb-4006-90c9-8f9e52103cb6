import { TrialCategory, UserTrialState, TrialStatus } from '../../../shared/types'

const TRIAL_STORAGE_KEY = 'user-trial-state'
const USER_INFO_STORAGE_KEY = 'userInfo'

interface UserInfo {
  token: string
  nickname: string
  avatar: string
  is_vip: number  // 1 for VIP, 0 for non-VIP
  vip_status: number
  vip?: {
    over_time: number
    over_time_date: string
    over_day: number
  }
}

class TrialService {
  private static instance: TrialService
  private trialState: UserTrialState

  private constructor() {
    this.trialState = this.loadTrialState()
    this.initializeUserInfoWatcher()
  }

  public static getInstance(): TrialService {
    if (!TrialService.instance) {
      TrialService.instance = new TrialService()
    }
    return TrialService.instance
  }

  private initializeUserInfoWatcher(): void {
    // 监听用户信息变化
    this.updateVipStatusFromUserInfo()
    
    // 监听localStorage变化
    window.addEventListener('storage', (event) => {
      if (event.key === USER_INFO_STORAGE_KEY) {
        this.updateVipStatusFromUserInfo()
      }
    })
  }

  private getCurrentUserInfo(): UserInfo | null {
    try {
      const userInfoStr = localStorage.getItem(USER_INFO_STORAGE_KEY)
      if (userInfoStr) {
        return JSON.parse(userInfoStr)
      }
    } catch (error) {
      console.warn('Failed to parse user info:', error)
    }
    return null
  }

  private updateVipStatusFromUserInfo(): void {
    const userInfo = this.getCurrentUserInfo()
    const newVipStatus = userInfo ? userInfo.is_vip === 1 : false
    
    // 如果VIP状态发生变化，更新试用状态
    if (this.trialState.isVip !== newVipStatus) {
      console.log('VIP status changed:', this.trialState.isVip, '->', newVipStatus)
      this.setVipStatus(newVipStatus)
    }
  }

  private loadTrialState(): UserTrialState {
    try {
      const stored = localStorage.getItem(TRIAL_STORAGE_KEY)
      if (stored) {
        const state = JSON.parse(stored)
        // 检查当前用户VIP状态
        const userInfo = this.getCurrentUserInfo()
        const currentVipStatus = userInfo ? userInfo.is_vip === 1 : false
        
        // 如果存储的VIP状态与当前不一致，优先使用当前VIP状态
        if (state.isVip !== currentVipStatus) {
          state.isVip = currentVipStatus
          // 如果变成VIP，重置试用状态
          if (currentVipStatus) {
            state.trialStatus = {
              VIDEO: false,
              AUDIO: false,
              IMAGE: false
            }
          }
          state.lastUpdated = new Date().toISOString()
        }
        
        return state
      }
    } catch (error) {
      console.warn('Failed to load trial state:', error)
    }

    // 获取当前用户VIP状态
    const userInfo = this.getCurrentUserInfo()
    const isVip = userInfo ? userInfo.is_vip === 1 : false

    // 默认试用状态
    return {
      isVip,
      trialStatus: {
        VIDEO: false,
        AUDIO: false,
        IMAGE: false
      },
      lastUpdated: new Date().toISOString()
    }
  }

  private saveTrialState(): void {
    try {
      localStorage.setItem(TRIAL_STORAGE_KEY, JSON.stringify(this.trialState))
    } catch (error) {
      console.error('Failed to save trial state:', error)
    }
  }

  public getTrialState(): UserTrialState {
    return { ...this.trialState }
  }

  public isVip(): boolean {
    return this.trialState.isVip
  }

  public setVipStatus(isVip: boolean): void {
    this.trialState.isVip = isVip
    this.trialState.lastUpdated = new Date().toISOString()
    
    // If upgrading to VIP, reset all trial status
    if (isVip) {
      this.trialState.trialStatus = {
        VIDEO: false,
        AUDIO: false,
        IMAGE: false
      }
    }
    
    this.saveTrialState()
    
    // Trigger storage event to update UI
    window.dispatchEvent(new StorageEvent('storage', {
      key: TRIAL_STORAGE_KEY,
      newValue: JSON.stringify(this.trialState)
    }))
  }

  public canUseCategory(category: TrialCategory): boolean {
    // VIP users can always use any category
    if (this.trialState.isVip) {
      return true
    }

    // Non-VIP users can only use categories they haven't tried yet
    return !this.trialState.trialStatus[category]
  }

  public markCategoryUsed(category: TrialCategory): void {
    if (!this.trialState.isVip) {
      this.trialState.trialStatus[category] = true
      this.trialState.lastUpdated = new Date().toISOString()
      this.saveTrialState()
      
      // Trigger storage event to update UI
      window.dispatchEvent(new StorageEvent('storage', {
        key: TRIAL_STORAGE_KEY,
        newValue: JSON.stringify(this.trialState)
      }))
    }
  }

  public getRemainingTrials(): { category: TrialCategory; available: boolean }[] {
    if (this.trialState.isVip) {
      return [
        { category: 'VIDEO', available: true },
        { category: 'AUDIO', available: true },
        { category: 'IMAGE', available: true }
      ]
    }

    return [
      { category: 'VIDEO', available: !this.trialState.trialStatus.VIDEO },
      { category: 'AUDIO', available: !this.trialState.trialStatus.AUDIO },
      { category: 'IMAGE', available: !this.trialState.trialStatus.IMAGE }
    ]
  }

  public resetTrials(): void {
    this.trialState.trialStatus = {
      VIDEO: false,
      AUDIO: false,
      IMAGE: false
    }
    this.trialState.lastUpdated = new Date().toISOString()
    this.saveTrialState()
    
    // Trigger storage event to update UI
    window.dispatchEvent(new StorageEvent('storage', {
      key: TRIAL_STORAGE_KEY,
      newValue: JSON.stringify(this.trialState)
    }))
  }

  public forceRefreshTrialState(): void {
    // 重新获取用户VIP状态并更新试用状态
    this.updateVipStatusFromUserInfo()
    
    // 触发存储事件更新UI
    window.dispatchEvent(new StorageEvent('storage', {
      key: TRIAL_STORAGE_KEY,
      newValue: JSON.stringify(this.trialState)
    }))
  }

  public syncUserVipStatus(): void {
    // 手动同步用户VIP状态，供登录成功后调用
    this.updateVipStatusFromUserInfo()
  }

  public getCategoryDisplayName(category: TrialCategory): string {
    const names = {
      VIDEO: '视频转换',
      AUDIO: '音频转换',
      IMAGE: '图片转换'
    }
    return names[category]
  }
}

export default TrialService
