// Test additional ImageConverter features
const { ImageConverter } = require('./out/main/imageConverter-B5wy5sl6.js')
const sharp = require('sharp')
const fs = require('fs')
const path = require('path')

async function testImageFeatures() {
  try {
    console.log('Testing ImageConverter additional features...')
    
    // Initialize the converter
    await ImageConverter.initialize()
    console.log('✓ ImageConverter initialized successfully')
    
    // Create a test image (200x200 blue square)
    const testImagePath = path.join(__dirname, 'test-large.png')
    const optimizedPath = path.join(__dirname, 'test-optimized.jpg')
    const resizedPath = path.join(__dirname, 'test-resized.png')
    
    await sharp({
      create: {
        width: 200,
        height: 200,
        channels: 3,
        background: { r: 0, g: 0, b: 255 }
      }
    })
    .png()
    .toFile(testImagePath)
    
    console.log('✓ Test image created (200x200)')
    
    // Test image optimization
    await ImageConverter.optimizeImage(testImagePath, optimizedPath, 60)
    console.log('✓ Image optimization completed')
    
    // Test image resizing
    await ImageConverter.resizeImage(testImagePath, resizedPath, 100, 100)
    console.log('✓ Image resizing completed')
    
    // Verify files and get info
    if (fs.existsSync(optimizedPath)) {
      const optimizedInfo = await ImageConverter.getImageInfo(optimizedPath)
      console.log('✓ Optimized image info:', {
        width: optimizedInfo.width,
        height: optimizedInfo.height,
        format: optimizedInfo.format
      })
    }
    
    if (fs.existsSync(resizedPath)) {
      const resizedInfo = await ImageConverter.getImageInfo(resizedPath)
      console.log('✓ Resized image info:', {
        width: resizedInfo.width,
        height: resizedInfo.height,
        format: resizedInfo.format
      })
    }
    
    // Test different format conversions
    const webpPath = path.join(__dirname, 'test-webp.webp')
    await ImageConverter.convert(testImagePath, webpPath, 'png', 'webp', { quality: 85 })
    console.log('✓ PNG to WebP conversion completed')
    
    const tiffPath = path.join(__dirname, 'test-tiff.tiff')
    await ImageConverter.convert(testImagePath, tiffPath, 'png', 'tiff', { quality: 90 })
    console.log('✓ PNG to TIFF conversion completed')
    
    // Cleanup
    const filesToClean = [testImagePath, optimizedPath, resizedPath, webpPath, tiffPath]
    filesToClean.forEach(file => {
      if (fs.existsSync(file)) fs.unlinkSync(file)
    })
    
    console.log('✓ All additional features tested successfully!')
    
  } catch (error) {
    console.error('✗ Test failed:', error)
  }
}

testImageFeatures()