import React, { Component, ErrorInfo, ReactNode } from 'react'
import { AlertT<PERSON>gle, RefreshCw, Home } from 'lucide-react'
import { ErrorManager } from '../utils/errorManager'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    }
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    })

    // 报告错误到错误管理器
    const errorManager = ErrorManager.getInstance()
    errorManager.reportError(error, {
      componentStack: errorInfo.componentStack,
      errorBoundary: true
    })

    console.error('<PERSON>rrorBoundary caught an error:', error, errorInfo)
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    })
  }

  handleGoHome = () => {
    // 重置状态并导航到首页
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    })
    
    // 如果有路由，可以导航到首页
    window.location.hash = '#/'
  }

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义 fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback
      }

      // 默认错误 UI
      return (
        <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center p-8">
          <div className="bg-white rounded-lg shadow-2xl p-8 max-w-2xl w-full">
            {/* Error Icon */}
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                <AlertTriangle className="w-8 h-8 text-red-500" />
              </div>
            </div>

            {/* Error Title */}
            <div className="text-center mb-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                应用出现了问题
              </h1>
              <p className="text-gray-600">
                很抱歉，应用遇到了意外错误。您可以尝试刷新页面或返回首页。
              </p>
            </div>

            {/* Error Details (collapsible) */}
            {this.state.error && (
              <details className="mb-6 bg-gray-50 rounded-lg p-4">
                <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900">
                  查看错误详情
                </summary>
                <div className="mt-3 space-y-3">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-1">错误消息</h4>
                    <p className="text-sm text-red-600 font-mono bg-red-50 p-2 rounded">
                      {this.state.error.message}
                    </p>
                  </div>
                  
                  {this.state.error.stack && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-1">错误堆栈</h4>
                      <pre className="text-xs text-gray-600 bg-gray-100 p-2 rounded overflow-x-auto max-h-32">
                        {this.state.error.stack}
                      </pre>
                    </div>
                  )}
                  
                  {this.state.errorInfo?.componentStack && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-1">组件堆栈</h4>
                      <pre className="text-xs text-gray-600 bg-gray-100 p-2 rounded overflow-x-auto max-h-32">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </div>
                  )}
                </div>
              </details>
            )}

            {/* Action Buttons */}
            <div className="flex items-center justify-center space-x-4">
              <button
                onClick={this.handleRetry}
                className="flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <RefreshCw className="w-4 h-4" />
                <span>重试</span>
              </button>
              
              <button
                onClick={this.handleGoHome}
                className="flex items-center space-x-2 px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                <Home className="w-4 h-4" />
                <span>返回首页</span>
              </button>
            </div>

            {/* Help Text */}
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-500">
                如果问题持续存在，请尝试重新启动应用或联系技术支持。
              </p>
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary