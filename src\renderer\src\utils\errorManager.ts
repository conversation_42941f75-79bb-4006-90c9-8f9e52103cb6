import { ErrorType, ErrorSeverity } from '../../../shared/errors'

export interface ErrorInfo {
  id: string
  type: ErrorType
  message: string
  severity: ErrorSeverity
  recoverable: boolean
  timestamp: string
  details?: any
}

export interface ErrorNotification {
  id: string
  title: string
  message: string
  type: 'error' | 'warning' | 'info'
  duration?: number
  actions?: Array<{
    label: string
    action: () => void
  }>
}

export class ErrorManager {
  private static instance: ErrorManager
  private errorHistory: ErrorInfo[] = []
  private errorCallbacks: Array<(error: ErrorInfo) => void> = []
  private notificationCallbacks: Array<(notification: ErrorNotification) => void> = []

  private constructor() {
    this.setupErrorListeners()
  }

  static getInstance(): ErrorManager {
    if (!ErrorManager.instance) {
      ErrorManager.instance = new ErrorManager()
    }
    return ErrorManager.instance
  }

  // 设置错误监听器
  private setupErrorListeners(): void {
    // 监听来自主进程的错误事件
    if (window.electron?.ipcRenderer) {
      window.electron.ipcRenderer.on('error-occurred', (_event, errorData) => {
        this.handleError(errorData)
      })

      window.electron.ipcRenderer.on('critical-error', (_event, data) => {
        this.handleCriticalError(data.error, data.requiresRestart)
      })

      window.electron.ipcRenderer.on('high-severity-error', (_event, data) => {
        this.handleHighSeverityError(data.error)
      })
    }

    // 监听未捕获的 JavaScript 错误
    window.addEventListener('error', (event) => {
      this.handleJavaScriptError(event.error, event.filename, event.lineno, event.colno)
    })

    // 监听未处理的 Promise 拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.handleUnhandledRejection(event.reason)
    })
  }

  // 处理错误
  handleError(errorData: ErrorInfo): void {
    // 添加到历史记录
    this.errorHistory.push(errorData)
    
    // 限制历史记录数量
    if (this.errorHistory.length > 100) {
      this.errorHistory = this.errorHistory.slice(-100)
    }

    // 通知所有监听器
    this.errorCallbacks.forEach(callback => {
      try {
        callback(errorData)
      } catch (err) {
        console.error('Error in error callback:', err)
      }
    })

    // 根据严重程度创建通知
    this.createErrorNotification(errorData)
  }

  // 处理严重错误
  private handleCriticalError(error: ErrorInfo, requiresRestart: boolean): void {
    this.handleError(error)

    // 显示严重错误对话框
    const notification: ErrorNotification = {
      id: `critical-${error.id}`,
      title: '严重错误',
      message: requiresRestart 
        ? '应用遇到严重错误，需要重新启动' 
        : '应用遇到严重错误，但可以继续使用',
      type: 'error',
      duration: requiresRestart ? undefined : 10000,
      actions: requiresRestart ? [
        {
          label: '重新启动',
          action: () => {
            if (window.electron?.ipcRenderer) {
              window.electron.ipcRenderer.invoke('app-restart')
            }
          }
        }
      ] : undefined
    }

    this.showNotification(notification)
  }

  // 处理高严重性错误
  private handleHighSeverityError(error: ErrorInfo): void {
    this.handleError(error)
  }

  // 处理 JavaScript 错误
  private handleJavaScriptError(error: Error, filename?: string, lineno?: number, colno?: number): void {
    const errorInfo: ErrorInfo = {
      id: `js-error-${Date.now()}`,
      type: ErrorType.UNKNOWN_ERROR,
      message: error.message,
      severity: ErrorSeverity.MEDIUM,
      recoverable: true,
      timestamp: new Date().toISOString(),
      details: {
        filename,
        lineno,
        colno,
        stack: error.stack
      }
    }

    this.handleError(errorInfo)
  }

  // 处理未处理的 Promise 拒绝
  private handleUnhandledRejection(reason: any): void {
    const errorInfo: ErrorInfo = {
      id: `promise-rejection-${Date.now()}`,
      type: ErrorType.UNKNOWN_ERROR,
      message: reason instanceof Error ? reason.message : String(reason),
      severity: ErrorSeverity.MEDIUM,
      recoverable: true,
      timestamp: new Date().toISOString(),
      details: {
        reason: reason instanceof Error ? reason.stack : reason
      }
    }

    this.handleError(errorInfo)
  }

  // 创建错误通知
  private createErrorNotification(error: ErrorInfo): void {
    let notificationType: 'error' | 'warning' | 'info' = 'error'
    let duration: number | undefined = 5000

    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        notificationType = 'error'
        duration = undefined // 不自动消失
        break
      case ErrorSeverity.HIGH:
        notificationType = 'error'
        duration = 8000
        break
      case ErrorSeverity.MEDIUM:
        notificationType = 'warning'
        duration = 5000
        break
      case ErrorSeverity.LOW:
        notificationType = 'info'
        duration = 3000
        break
    }

    const notification: ErrorNotification = {
      id: `error-${error.id}`,
      title: this.getErrorTitle(error.type),
      message: this.getShortErrorMessage(error),
      type: notificationType,
      duration,
      actions: error.recoverable ? [
        {
          label: '查看详情',
          action: () => {
            // 触发显示错误详情对话框
            this.showErrorDetails(error)
          }
        }
      ] : undefined
    }

    this.showNotification(notification)
  }

  // 获取错误标题
  private getErrorTitle(errorType: ErrorType): string {
    const titles: Record<ErrorType, string> = {
      [ErrorType.FILE_NOT_FOUND]: '文件未找到',
      [ErrorType.FILE_ACCESS_DENIED]: '文件访问被拒绝',
      [ErrorType.FILE_TOO_LARGE]: '文件过大',
      [ErrorType.INVALID_FILE_FORMAT]: '文件格式无效',
      [ErrorType.CONVERSION_FAILED]: '转换失败',
      [ErrorType.FFMPEG_NOT_FOUND]: 'FFmpeg 未找到',
      [ErrorType.SHARP_NOT_AVAILABLE]: 'Sharp 不可用',
      [ErrorType.UNSUPPORTED_FORMAT]: '格式不支持',
      [ErrorType.INVALID_CONVERSION_OPTIONS]: '转换选项无效',
      [ErrorType.INSUFFICIENT_DISK_SPACE]: '磁盘空间不足',
      [ErrorType.NETWORK_ERROR]: '网络错误',
      [ErrorType.PERMISSION_DENIED]: '权限不足',
      [ErrorType.INITIALIZATION_FAILED]: '初始化失败',
      [ErrorType.UNKNOWN_ERROR]: '未知错误'
    }

    return titles[errorType] || '错误'
  }

  // 获取简短的错误消息
  private getShortErrorMessage(error: ErrorInfo): string {
    // 截取前100个字符作为简短消息
    return error.message.length > 100 
      ? error.message.substring(0, 100) + '...' 
      : error.message
  }

  // 显示通知
  private showNotification(notification: ErrorNotification): void {
    this.notificationCallbacks.forEach(callback => {
      try {
        callback(notification)
      } catch (err) {
        console.error('Error in notification callback:', err)
      }
    })
  }

  // 显示错误详情（由具体的 UI 组件实现）
  private showErrorDetails(error: ErrorInfo): void {
    // 这里可以触发显示错误详情对话框的事件
    const event = new CustomEvent('show-error-details', { detail: error })
    window.dispatchEvent(event)
  }

  // 注册错误回调
  onError(callback: (error: ErrorInfo) => void): () => void {
    this.errorCallbacks.push(callback)
    return () => {
      const index = this.errorCallbacks.indexOf(callback)
      if (index > -1) {
        this.errorCallbacks.splice(index, 1)
      }
    }
  }

  // 注册通知回调
  onNotification(callback: (notification: ErrorNotification) => void): () => void {
    this.notificationCallbacks.push(callback)
    return () => {
      const index = this.notificationCallbacks.indexOf(callback)
      if (index > -1) {
        this.notificationCallbacks.splice(index, 1)
      }
    }
  }

  // 获取错误历史
  getErrorHistory(): ErrorInfo[] {
    return [...this.errorHistory]
  }

  // 清除错误历史
  clearErrorHistory(): void {
    this.errorHistory = []
  }

  // 获取错误统计
  getErrorStats(): {
    total: number
    bySeverity: Record<ErrorSeverity, number>
    byType: Record<ErrorType, number>
    recent: ErrorInfo[]
  } {
    const bySeverity = {
      [ErrorSeverity.LOW]: 0,
      [ErrorSeverity.MEDIUM]: 0,
      [ErrorSeverity.HIGH]: 0,
      [ErrorSeverity.CRITICAL]: 0
    }

    const byType: Record<ErrorType, number> = {} as any

    this.errorHistory.forEach(error => {
      bySeverity[error.severity]++
      byType[error.type] = (byType[error.type] || 0) + 1
    })

    // 获取最近的错误（最近1小时）
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString()
    const recent = this.errorHistory.filter(error => error.timestamp > oneHourAgo)

    return {
      total: this.errorHistory.length,
      bySeverity,
      byType,
      recent
    }
  }

  // 手动报告错误
  reportError(error: Error, context?: any): void {
    const errorInfo: ErrorInfo = {
      id: `manual-${Date.now()}`,
      type: ErrorType.UNKNOWN_ERROR,
      message: error.message,
      severity: ErrorSeverity.MEDIUM,
      recoverable: true,
      timestamp: new Date().toISOString(),
      details: {
        stack: error.stack,
        context
      }
    }

    this.handleError(errorInfo)
  }
}