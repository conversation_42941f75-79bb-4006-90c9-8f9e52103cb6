import React, { useState, useEffect } from 'react'
import { Arrow<PERSON><PERSON><PERSON>, Folder, Palette, Zap, Info, TestTube } from 'lucide-react'
import { SettingsService } from '../services/settingsService'
import { IPCTest } from '../utils/ipcTest'
import { useTheme } from '../contexts/ThemeContext'
import type { AppSettings } from '../../../shared/types'

interface SettingsPageProps {
  onBack: () => void
}

const SettingsPage: React.FC<SettingsPageProps> = ({ onBack }) => {
  const { theme, setTheme } = useTheme()
  const [settings, setSettings] = useState<AppSettings>({
    outputPath: '',
    theme: 'dark',
    autoOpenOutput: true,
    maxConcurrentTasks: 3
  })
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [ffmpegStatus, setFfmpegStatus] = useState<'checking' | 'available' | 'unavailable'>('checking')
  const [appVersion, setAppVersion] = useState('1.0.0')

  useEffect(() => {
    loadSettings()
    checkFFmpegStatus()
    loadAppVersion()
  }, [])

  const checkFFmpegStatus = async () => {
    try {
      // Try to get supported formats to check if FFmpeg is available
      await SettingsService.checkFFmpegStatus()
      setFfmpegStatus('available')
    } catch (error) {
      console.error('FFmpeg not available:', error)
      setFfmpegStatus('unavailable')
    }
  }

  const loadAppVersion = async () => {
    try {
      const appInfo = await SettingsService.getAppVersion()
      setAppVersion(appInfo.version)
    } catch (error) {
      console.error('Failed to load app version:', error)
    }
  }

  const loadSettings = async () => {
    try {
      const currentSettings = await SettingsService.getSettings()
      setSettings(currentSettings)
    } catch (error) {
      console.error('Failed to load settings:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const saveSettings = async (newSettings: Partial<AppSettings>) => {
    setIsSaving(true)
    try {
      const updatedSettings = { ...settings, ...newSettings }
      await SettingsService.updateSettings(newSettings)
      setSettings(updatedSettings)
    } catch (error) {
      console.error('Failed to save settings:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const handleOutputPathChange = async () => {
    try {
      const result = await SettingsService.selectOutputFolder()
      if (result && !result.canceled && result.filePaths.length > 0) {
        const selectedPath = result.filePaths[0]
        await saveSettings({ outputPath: selectedPath })
      }
    } catch (error) {
      console.error('Failed to select output folder:', error)
    }
  }

  if (isLoading) {
    const backgroundClasses = theme === 'dark' 
      ? 'bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900'
      : 'bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100'
    
    const backgroundElementClasses = theme === 'dark' ? 'opacity-10' : 'opacity-15'
    const textClasses = theme === 'dark' ? 'text-white' : 'text-gray-800'
    const progressBgClasses = theme === 'dark' ? 'bg-gray-700' : 'bg-gray-300'

    return (
      <div className={`min-h-screen ${backgroundClasses} flex items-center justify-center relative overflow-hidden transition-all duration-500`}>
        {/* Static Background Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className={`absolute -top-40 -right-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl ${backgroundElementClasses} transition-opacity duration-500`}></div>
          <div className={`absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl ${backgroundElementClasses} transition-opacity duration-500`}></div>
        </div>
        
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-glow">
            <span className="text-2xl">⚙️</span>
          </div>
          <div className={`${textClasses} text-xl font-medium`}>加载设置中...</div>
          <div className={`mt-4 w-32 h-1 ${progressBgClasses} rounded-full mx-auto overflow-hidden`}>
            <div className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-shimmer"></div>
          </div>
        </div>
      </div>
    )
  }

  // Theme-specific classes
  const backgroundClasses = theme === 'dark' 
    ? 'bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900'
    : 'bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100'
  
  const backgroundElementClasses = theme === 'dark' ? 'opacity-10' : 'opacity-15'
  const textClasses = theme === 'dark' ? 'text-white' : 'text-gray-800'
  const subtextClasses = theme === 'dark' ? 'text-gray-300' : 'text-gray-600'
  const cardClasses = theme === 'dark' 
    ? 'bg-white bg-opacity-5 backdrop-blur-md border-white border-opacity-10 hover:border-opacity-20'
    : 'bg-white bg-opacity-80 backdrop-blur-md border-gray-200 border-opacity-50 hover:border-opacity-80'

  return (
    <div className={`min-h-screen ${backgroundClasses} p-8 relative overflow-hidden transition-all duration-500`}>
      {/* Static Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className={`absolute -top-40 -right-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl ${backgroundElementClasses} transition-opacity duration-500`}></div>
        <div className={`absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl ${backgroundElementClasses} transition-opacity duration-500`}></div>
      </div>

      <div className="max-w-4xl mx-auto relative z-10">
        {/* Header */}
        <div className="flex items-center mb-8">
          <button
            onClick={onBack}
            className={`flex items-center ${textClasses} hover:text-blue-300 transition-all duration-300 mr-6 group hover:scale-105`}
          >
            <ArrowLeft className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
            <span className="font-medium">返回</span>
          </button>
          <div className="flex items-center">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mr-4 shadow-glow">
              <span className="text-xl">⚙️</span>
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">应用设置</h1>
              <p className={subtextClasses}>自定义您的转换体验</p>
            </div>
          </div>
        </div>

        <div className="space-y-8">
          {/* Output Settings */}
          <div className={`${cardClasses} rounded-2xl p-6 border transition-all duration-300 hover:scale-101 hover:shadow-glow-sm`}>
            <div className="flex items-center mb-6">
              <Folder className="w-6 h-6 text-blue-400 mr-3" />
              <h2 className={`text-xl font-semibold ${textClasses}`}>输出设置</h2>
            </div>

            <div className="space-y-6">
              <div>
                <label className={`block ${textClasses} font-medium mb-3`}>默认输出路径</label>
                <div className="flex items-center space-x-3">
                  <div className={`flex-1 ${theme === 'dark' ? 'bg-gray-700 hover:bg-gray-600 border-gray-600 hover:border-gray-500' : 'bg-gray-100 hover:bg-gray-50 border-gray-300 hover:border-gray-400'} rounded-lg px-4 py-3 ${textClasses} transition-all duration-300 border`}>
                    {settings.outputPath || '~/Desktop/格式转换'}
                  </div>
                  <button
                    onClick={handleOutputPathChange}
                    className="px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-glow-sm group relative overflow-hidden"
                  >
                    {/* Button shimmer effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white via-opacity-10 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer transition-opacity duration-300"></div>
                    <span className="relative z-10">选择文件夹</span>
                  </button>
                </div>
                <p className={`${subtextClasses} text-sm mt-2`}>
                  转换完成的文件将保存到此目录
                </p>
              </div>

              {/* <div className="flex items-center justify-between">
                <div>
                  <h3 className={`${textClasses} font-medium`}>转换完成后自动打开输出目录</h3>
                  <p className={`${subtextClasses} text-sm`}>方便您快速查看转换结果</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer group">
                  <input
                    type="checkbox"
                    checked={settings.autoOpenOutput}
                    onChange={(e) => saveSettings({ autoOpenOutput: e.target.checked })}
                    className="sr-only peer"
                  />
                  <div className={`w-11 h-6 ${theme === 'dark' ? 'bg-gray-600' : 'bg-gray-300'} peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600 group-hover:scale-105 transition-transform duration-300`}></div>
                </label>
              </div> */}
            </div>
          </div>

          {/* Appearance Settings */}
          <div className={`${cardClasses} rounded-2xl p-6 border transition-all duration-300 hover:scale-101 hover:shadow-glow-sm`}>
            <div className="flex items-center mb-6">
              <Palette className="w-6 h-6 text-purple-400 mr-3" />
              <h2 className={`text-xl font-semibold ${textClasses}`}>外观设置</h2>
            </div>

            <div className="space-y-6">
              <div>
                <label className={`block ${textClasses} font-medium mb-3`}>主题</label>
                <div className="grid grid-cols-2 gap-3">
                  <button
                    onClick={() => setTheme('dark')}
                    className={`p-4 rounded-lg border-2 transition-all duration-300 hover:scale-102 group relative overflow-hidden ${
                      theme === 'dark'
                        ? 'border-blue-500 bg-blue-500 bg-opacity-20 shadow-glow-sm'
                        : 'border-gray-600 hover:border-gray-500'
                    }`}
                  >
                    {/* Shimmer effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white via-opacity-5 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer transition-opacity duration-300"></div>
                    
                    <div className="w-full h-8 bg-gradient-to-r from-gray-800 to-gray-900 rounded mb-2 relative z-10"></div>
                    <span className="text-white dark:text-white font-medium relative z-10">深色主题</span>
                    
                    {theme === 'dark' && (
                      <div className="absolute top-2 right-2 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                      </div>
                    )}
                  </button>
                  <button
                    onClick={() => setTheme('light')}
                    className={`p-4 rounded-lg border-2 transition-all duration-300 hover:scale-102 group relative overflow-hidden ${
                      theme === 'light'
                        ? 'border-blue-500 bg-blue-500 bg-opacity-20 shadow-glow-sm'
                        : 'border-gray-600 hover:border-gray-500'
                    }`}
                  >
                    {/* Shimmer effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white via-opacity-5 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer transition-opacity duration-300"></div>
                    
                    <div className="w-full h-8 bg-gradient-to-r from-gray-100 to-gray-200 rounded mb-2 relative z-10"></div>
                    <span className="text-white dark:text-white font-medium relative z-10">浅色主题</span>
                    
                    {theme === 'light' && (
                      <div className="absolute top-2 right-2 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                      </div>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Performance Settings */}
          <div className={`${cardClasses} rounded-2xl p-6 border transition-all duration-300 hover:scale-101 hover:shadow-glow-sm`}>
            <div className="flex items-center mb-6">
              <Zap className="w-6 h-6 text-yellow-400 mr-3" />
              <h2 className={`text-xl font-semibold ${textClasses}`}>性能设置</h2>
            </div>

            <div className="space-y-6">
              <div>
                <label className={`block ${textClasses} font-medium mb-3`}>
                  最大并发转换任务数: <span className="text-yellow-400 font-bold">{settings.maxConcurrentTasks}</span>
                </label>
                <div className="relative">
                  <input
                    type="range"
                    min="1"
                    max="8"
                    value={settings.maxConcurrentTasks}
                    onChange={(e) => saveSettings({ maxConcurrentTasks: parseInt(e.target.value) })}
                    className={`w-full h-3 ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-300'} rounded-lg appearance-none cursor-pointer slider hover:scale-102 transition-transform duration-300`}
                    style={{
                      background: `linear-gradient(to right, #eab308 0%, #eab308 ${((settings.maxConcurrentTasks - 1) / 7) * 100}%, ${theme === 'dark' ? '#374151' : '#d1d5db'} ${((settings.maxConcurrentTasks - 1) / 7) * 100}%, ${theme === 'dark' ? '#374151' : '#d1d5db'} 100%)`
                    }}
                  />
                  {/* Slider thumb glow effect */}
                  <div 
                    className="absolute top-1/2 transform -translate-y-1/2 w-5 h-5 bg-yellow-400 rounded-full shadow-glow-sm pointer-events-none"
                    style={{ left: `calc(${((settings.maxConcurrentTasks - 1) / 7) * 100}% - 10px)` }}
                  ></div>
                </div>
                <div className={`flex justify-between ${subtextClasses} text-sm mt-2`}>
                  <span>1 (节能)</span>
                  <span>8 (高性能)</span>
                </div>
                <p className={`${subtextClasses} text-sm mt-2`}>
                  更多并发任务可以提高转换速度，但会消耗更多系统资源
                </p>
              </div>
            </div>
          </div>

          {/* Developer Tools (only in development) */}
          {process.env.NODE_ENV === 'development' && (
            <div className={`${cardClasses} rounded-2xl p-6 border transition-all duration-300 hover:scale-101 hover:shadow-glow-sm`}>
              <div className="flex items-center mb-6">
                <TestTube className="w-6 h-6 text-orange-400 mr-3" />
                <h2 className={`text-xl font-semibold ${textClasses}`}>开发者工具</h2>
              </div>

              <div className="space-y-4">
                <div>
                  <h3 className={`${textClasses} font-medium mb-2`}>IPC 通信测试</h3>
                  <p className={`${subtextClasses} text-sm mb-4`}>
                    测试主进程与渲染进程之间的 IPC 通信是否正常工作
                  </p>
                  <button
                    onClick={() => IPCTest.runFullTest()}
                    className="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-glow-sm group relative overflow-hidden"
                  >
                    {/* Button shimmer effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white via-opacity-10 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer transition-opacity duration-300"></div>
                    <span className="relative z-10">运行 IPC 测试</span>
                  </button>
                </div>
                <div className={`${subtextClasses} text-xs`}>
                  测试结果将显示在开发者控制台中 (F12)
                </div>
              </div>
            </div>
          )}

          {/* System Status */}
          <div className={`${cardClasses} rounded-2xl p-6 border transition-all duration-300 hover:scale-101 hover:shadow-glow-sm`}>
            <div className="flex items-center mb-6">
              <Info className="w-6 h-6 text-blue-400 mr-3" />
              <h2 className={`text-xl font-semibold ${textClasses}`}>系统状态</h2>
            </div>

            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className={subtextClasses}>FFmpeg 状态</span>
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${
                    ffmpegStatus === 'checking' ? 'bg-yellow-400' :
                    ffmpegStatus === 'available' ? 'bg-green-400' : 'bg-red-400'
                  }`}></div>
                  <span className={`${textClasses} font-medium text-sm`}>
                    {ffmpegStatus === 'checking' ? '检查中...' :
                     ffmpegStatus === 'available' ? '可用' : '不可用'}
                  </span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className={subtextClasses}>视频转换</span>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                  <span className={`${textClasses} font-medium`}>支持</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className={subtextClasses}>音频转换</span>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                  <span className={`${textClasses} font-medium`}>支持</span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className={subtextClasses}>图片转换</span>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                  <span className={`${textClasses} font-medium`}>支持</span>
                </div>
              </div>
            </div>
          </div>

          {/* About */}
          <div className={`${cardClasses} rounded-2xl p-6 border transition-all duration-300 hover:scale-101 hover:shadow-glow-sm`}>
            <div className="flex items-center mb-6">
              <Info className="w-6 h-6 text-green-400 mr-3" />
              <h2 className={`text-xl font-semibold ${textClasses}`}>关于</h2>
            </div>

            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className={subtextClasses}>应用版本</span>
                <span className={`${textClasses} font-medium bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent`}>{appVersion}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className={subtextClasses}>支持的格式</span>
                <span className={`${textClasses} font-medium`}>视频、音频、图片</span>
              </div>
              {/* <div className="flex justify-between items-center">
                <span className={subtextClasses}>开发者</span>
                <span className={`${textClasses} font-medium`}>Format Converter Team</span>
              </div> */}
            </div>
          </div>
        </div>

        {/* Save Status */}
        {isSaving && (
          <div className="fixed bottom-8 right-8 bg-blue-600 text-white px-4 py-3 rounded-lg shadow-glow animate-fade-in-scale backdrop-blur-md border border-blue-400 border-opacity-30">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              <span className="font-medium">正在保存设置...</span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default SettingsPage