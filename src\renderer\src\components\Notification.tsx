import React, { useEffect } from 'react'
import { CheckCircle, XCircle, AlertCircle, Info } from 'lucide-react'

interface NotificationProps {
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  onClose: () => void
}

const Notification: React.FC<NotificationProps> = ({
  type,
  title,
  message,
  duration = 5000,
  onClose
}) => {
  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(onClose, duration)
      return () => clearTimeout(timer)
    }
    return undefined
  }, [duration, onClose])

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-6 h-6 text-green-400" />
      case 'error':
        return <XCircle className="w-6 h-6 text-red-400" />
      case 'warning':
        return <AlertCircle className="w-6 h-6 text-yellow-400" />
      case 'info':
        return <Info className="w-6 h-6 text-blue-400" />
    }
  }

  const getBackgroundColor = () => {
    switch (type) {
      case 'success':
        return 'bg-green-500 bg-opacity-20 border-green-500'
      case 'error':
        return 'bg-red-500 bg-opacity-20 border-red-500'
      case 'warning':
        return 'bg-yellow-500 bg-opacity-20 border-yellow-500'
      case 'info':
        return 'bg-blue-500 bg-opacity-20 border-blue-500'
    }
  }

  return (
    <div className="fixed top-10 right-4 z-50">
      <div className={`${getBackgroundColor()} border rounded-lg p-4 max-w-sm backdrop-blur-md shadow-glow-sm hover:shadow-glow transition-all duration-300 hover:scale-102`}>
        {/* Background shimmer effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white via-opacity-5 to-transparent opacity-0 hover:opacity-100 hover:animate-shimmer transition-opacity duration-300 rounded-lg"></div>

        <div className="flex items-start space-x-3 relative z-10">
          <div className="flex-shrink-0">
            {getIcon()}
          </div>
          <div className="flex-1 min-w-0">
            <h4 className="text-white font-medium text-sm">{title}</h4>
            {message && (
              <p className="text-gray-300 text-xs mt-1">{message}</p>
            )}
          </div>
          <button
            onClick={onClose}
            className="flex-shrink-0 text-gray-400 hover:text-white transition-all duration-300 hover:scale-110 hover:rotate-90"
          >
            ✕
          </button>
        </div>

        {/* Progress bar for auto-dismiss */}
        {duration > 0 && (
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-white bg-opacity-20 rounded-b-lg overflow-hidden">
            <div
              className="h-full bg-white bg-opacity-40 rounded-b-lg transition-all ease-linear"
              style={{
                animation: `notification-shrink ${duration}ms linear forwards`,
                width: '100%'
              }}
            />
          </div>
        )}
      </div>

      <style>{`
        @keyframes notification-shrink {
          from { width: 100%; }
          to { width: 0%; }
        }
      `}</style>
    </div>
  )
}

export default Notification