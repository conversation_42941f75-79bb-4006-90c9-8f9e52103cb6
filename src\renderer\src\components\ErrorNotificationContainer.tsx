import React, { useState, useEffect } from 'react'
import ErrorNotification from './ErrorNotification'
import { ErrorManager, ErrorNotification as ErrorNotificationType } from '../utils/errorManager'

const ErrorNotificationContainer: React.FC = () => {
  const [notifications, setNotifications] = useState<ErrorNotificationType[]>([])

  useEffect(() => {
    const errorManager = ErrorManager.getInstance()

    // 监听新的通知
    const unsubscribe = errorManager.onNotification((notification) => {
      setNotifications(prev => {
        // 避免重复通知
        if (prev.some(n => n.id === notification.id)) {
          return prev
        }
        return [...prev, notification]
      })
    })

    return unsubscribe
  }, [])

  const handleCloseNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  if (notifications.length === 0) {
    return null
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {notifications.map(notification => (
        <ErrorNotification
          key={notification.id}
          notification={notification}
          onClose={handleCloseNotification}
        />
      ))}
    </div>
  )
}

export default ErrorNotificationContainer