import { IPCService } from './ipcService'
import { ConversionService } from './conversionService'
import type { ConversionOptions, SupportedFormat, FileType } from '../../../shared/types'

export class ImageService {
  // Basic image conversion
  static async convertImage(
    inputPath: string,
    outputPath: string,
    inputFormat: SupportedFormat,
    outputFormat: SupportedFormat,
    options?: ConversionOptions
  ): Promise<void> {
    await ConversionService.convertFile(
      inputPath,
      outputPath,
      inputFormat,
      outputFormat,
      'IMAGE' as FileType,
      options
    )
  }

  // Get image metadata
  static async getImageInfo(imagePath: string): Promise<{
    width?: number
    height?: number
    format?: string
    size?: number
    channels?: number
    density?: number
  }> {
    try {
      const metadata = await IPCService.getImageInfo(imagePath)
      return {
        width: metadata.width,
        height: metadata.height,
        format: metadata.format,
        size: metadata.size,
        channels: metadata.channels,
        density: metadata.density
      }
    } catch (error) {
      console.error('Failed to get image info:', error)
      throw error
    }
  }

  // Optimize image (reduce file size while maintaining quality)
  static async optimizeImage(
    inputPath: string,
    outputPath: string,
    quality: number = 80
  ): Promise<void> {
    try {
      await IPCService.optimizeImage(inputPath, outputPath, quality)
    } catch (error) {
      console.error('Failed to optimize image:', error)
      throw error
    }
  }

  // Resize image
  static async resizeImage(
    inputPath: string,
    outputPath: string,
    width?: number,
    height?: number
  ): Promise<void> {
    try {
      await IPCService.resizeImage(inputPath, outputPath, width, height)
    } catch (error) {
      console.error('Failed to resize image:', error)
      throw error
    }
  }

  // Batch convert images
  static async batchConvertImages(
    files: File[],
    outputFormat: SupportedFormat,
    options?: ConversionOptions,
    onProgress?: (current: number, total: number) => void
  ): Promise<void> {
    const total = files.length
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      const inputPath = IPCService.getPathForFile(file)
      
      // Generate output filename
      const fileName = file.name.split('.').slice(0, -1).join('.')
      const outputPath = `${fileName}.${outputFormat}`
      
      // Get input format from file extension
      const inputFormat = file.name.split('.').pop()?.toLowerCase() as SupportedFormat
      
      await this.convertImage(inputPath, outputPath, inputFormat, outputFormat, options)
      
      if (onProgress) {
        onProgress(i + 1, total)
      }
    }
  }

  // Get supported image formats
  static getSupportedFormats(): { input: string[], output: string[] } {
    return {
      input: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff', 'avif', 'heif', 'svg'],
      output: ['jpg', 'jpeg', 'png', 'webp', 'avif', 'tiff', 'bmp']
    }
  }

  // Get common image resolutions
  static getCommonResolutions(): { name: string, width: number, height: number }[] {
    return [
      { name: '4K Ultra HD', width: 3840, height: 2160 },
      { name: '2K QHD', width: 2560, height: 1440 },
      { name: 'Full HD', width: 1920, height: 1080 },
      { name: 'HD', width: 1280, height: 720 },
      { name: 'SD', width: 854, height: 480 },
      { name: 'Mobile', width: 640, height: 360 }
    ]
  }

  // Get quality presets
  static getQualityPresets(): { name: string, value: number }[] {
    return [
      { name: '最高质量', value: 95 },
      { name: '高质量', value: 85 },
      { name: '中等质量', value: 75 },
      { name: '低质量', value: 60 },
      { name: '最低质量', value: 40 }
    ]
  }

  // Calculate optimal quality based on file size target
  static calculateOptimalQuality(
    originalSize: number,
    targetSizeKB: number
  ): number {
    // Simple heuristic: reduce quality based on size reduction needed
    const reductionRatio = targetSizeKB * 1024 / originalSize
    
    if (reductionRatio >= 0.8) return 90
    if (reductionRatio >= 0.6) return 80
    if (reductionRatio >= 0.4) return 70
    if (reductionRatio >= 0.2) return 60
    return 50
  }

  // Validate image file
  static isValidImageFile(file: File): boolean {
    const supportedFormats = this.getSupportedFormats().input
    const extension = file.name.split('.').pop()?.toLowerCase()
    
    return extension ? supportedFormats.includes(extension) : false
  }

  // Get file size in human readable format
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}