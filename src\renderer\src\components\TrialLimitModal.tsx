import React from 'react'
import { X, Crown, MessageCircle } from 'lucide-react'
import { useTheme } from '../contexts/ThemeContext'

interface TrialLimitModalProps {
  isVisible: boolean
  categoryName: string
  onClose: () => void
  theme?: 'light' | 'dark'
}

const TrialLimitModal: React.FC<TrialLimitModalProps> = ({
  isVisible,
  categoryName,
  onClose,
  theme = 'dark'
}) => {
  const { theme: contextTheme } = useTheme()
  const currentTheme = theme || contextTheme

  if (!isVisible) return null

  // Theme-specific classes
  const modalClasses = currentTheme === 'dark'
    ? 'bg-gray-800 border-gray-600 text-white'
    : 'bg-white border-gray-300 text-gray-800'

  const buttonClasses = currentTheme === 'dark'
    ? 'text-gray-300 hover:text-white hover:bg-gray-700'
    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-200'

  const textClasses = currentTheme === 'dark' ? 'text-white' : 'text-gray-800'
  const subtextClasses = currentTheme === 'dark' ? 'text-gray-300' : 'text-gray-600'

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
      <div className={`${modalClasses} border rounded-xl shadow-glow max-w-md w-full mx-4 overflow-hidden animate-in fade-in zoom-in duration-300`}>
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b ${currentTheme === 'dark' ? 'border-gray-600' : 'border-gray-300'}`}>
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center shadow-glow">
              <Crown className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className={`${textClasses} text-lg font-semibold`}>试用次数已用完</h3>
              <p className={`${subtextClasses} text-sm`}>{categoryName}功能</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className={`${buttonClasses} p-2 rounded-full transition-all duration-200 hover:scale-110`}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-glow animate-pulse">
              <Crown className="w-8 h-8 text-white" />
            </div>
            <h4 className={`${textClasses} text-xl font-bold mb-2`}>升级VIP解锁全部功能</h4>
            <p className={`${subtextClasses} text-sm leading-relaxed`}>
              非VIP用户<span className='text-red-400'>每种转换功能</span> 只能试用一次。<br />
              点击<span className='text-yellow-400 font-medium'>顶部"开通VIP"</span>按钮立即升级，享受无限制转换、更快速度和更多高级功能。
            </p>
          </div>

          {/* VIP Benefits */}
          <div className="space-y-3 mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className={`${subtextClasses} text-sm`}>无限制格式转换</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              <span className={`${subtextClasses} text-sm`}>批量文件处理</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
              <span className={`${subtextClasses} text-sm`}>高级转换选项</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
              <span className={`${subtextClasses} text-sm`}>优先转换速度</span>
            </div>
          </div>

          {/* VIP Guide */}
          <div className={`p-4 rounded-lg border ${currentTheme === 'dark' ? 'bg-gradient-to-r from-yellow-900/30 to-orange-900/30 border-yellow-600/50' : 'bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-300'} mb-6`}>
            <div className="flex items-center space-x-3 mb-2">
              <Crown className="w-5 h-5 text-yellow-400" />
              <span className={`${textClasses} font-medium`}>立即开通VIP会员</span>
            </div>
            <p className={`${subtextClasses} text-sm`}>
              点击窗口顶部的<span className="text-yellow-500 font-medium">"开通VIP"</span>按钮，选择合适的套餐，扫码支付即可立即享受VIP特权！
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className={`p-6 border-t ${currentTheme === 'dark' ? 'border-gray-600' : 'border-gray-300'} flex justify-center`}>
          <button
            onClick={onClose}
            className="px-8 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg font-medium transition-all duration-300 hover:scale-105 hover:shadow-glow transform hover:-translate-y-0.5"
          >
            我知道了
          </button>
        </div>
      </div>
    </div>
  )
}

export default TrialLimitModal
