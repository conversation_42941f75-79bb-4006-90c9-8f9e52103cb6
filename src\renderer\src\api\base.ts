const baseURL = 'https://apiv2.qiangliangtech.com/v1'

interface RequestOptions extends RequestInit {
  params?: Record<string, string>
  data?: any
}

class ApiError extends Error {
  constructor(public status: number, public message: string) {
    super(message)
    this.name = 'ApiError'
  }
}

/**
 * 简化的fetch请求封装
 * @param endpoint API端点
 * @param options 请求选项
 * @returns Promise响应数据
 */
export async function request<T = any>(
  endpoint: string,
  options: RequestOptions = {}
): Promise<T> {
  const {
    params,
    data,
    headers: customHeaders,
    ...restOptions
  } = options

  // 构建URL
  let url = `${baseURL}${endpoint}`
  if (params) {
    const searchParams = new URLSearchParams(params)
    url += `?${searchParams.toString()}`
  }

  // 从localStorage获取token，displayname写死（需保证请求头为 ASCII 安全值）
  const token = localStorage.getItem('userToken') || localStorage.getItem('AUTH_TOKEN')
  const displayName = 'com.maruitech.formatconversionmaster'
  // 将非 ASCII 字符进行 URL 编码，避免 fetch 报非 ISO-8859-1 错误

  // 设置默认headers（仅使用 ASCII 安全值）
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    ...(token && { token: `${token}` }),
    ...(displayName && { displayname: displayName }),
    ...customHeaders,
  }

  // 准备请求体
  const body = data ? JSON.stringify(data) : undefined

  const response = await fetch(url, {
    headers,
    body,
    ...restOptions,
  })

  if (!response.ok) {
    // 401 状态码表示登录状态过期，清除本地存储的用户信息
    if (response.status === 401) {
      localStorage.removeItem('userToken');
      localStorage.removeItem('userInfo');
      localStorage.removeItem('AUTH_TOKEN');
    }
    throw new ApiError(response.status, `HTTP error! status: ${response.status}`)
  }

  // 尝试解析JSON，如果失败则返回原始响应
  let jsonResponse: any;
  try {
    jsonResponse = await response.json();
    
    // 检查后端返回的业务状态码，code 为 401 也表示登录状态过期
    if (jsonResponse && jsonResponse.code === 401) {
      localStorage.removeItem('userToken');
      localStorage.removeItem('userInfo');
      localStorage.removeItem('AUTH_TOKEN');
    }
    
    return jsonResponse as T;
  } catch {
    return response as unknown as T
  }
}

/**
 * GET请求快捷方式
 */
export function get<T = any>(endpoint: string, params?: Record<string, string>) {
  return request<T>(endpoint, { method: 'GET', params })
}

/**
 * POST请求快捷方式
 */
export function post<T = any>(endpoint: string, data?: any) {
  return request<T>(endpoint, { method: 'POST', data })
}

/**
 * PUT请求快捷方式
 */
export function put<T = any>(endpoint: string, data?: any) {
  return request<T>(endpoint, { method: 'PUT', data })
}

/**
 * DELETE请求快捷方式
 */
export function del<T = any>(endpoint: string) {
  return request<T>(endpoint, { method: 'DELETE' })
}

/**
 * PATCH请求快捷方式
 */
export function patch<T = any>(endpoint: string, data?: any) {
  return request<T>(endpoint, { method: 'PATCH', data })
}

