# 首页卡片命名优化设计文档

## 概述

本设计文档旨在解决当前首页转换卡片命名不够通俗易懂的问题。我们将采用基于使用场景的命名策略，用日常语言替代技术术语，让普通用户能够快速理解和选择合适的转换选项。

## 架构

### 命名策略架构

```
转换卡片命名 = 使用场景 + 简单描述
例如：
- "压缩文件" + "减小文件大小，节省空间"
- "社交分享" + "适合微信、QQ等平台分享"
- "网页使用" + "加载更快的网页图片"
```

### 分类逻辑

#### 图片转换分类

1. **压缩文件** (JPG) - 减小文件大小，节省存储空间
2. **社交分享** (WEBP) - 适合微信、QQ等社交平台
3. **高清保存** (PNG) - 保持图片清晰度，无损压缩
4. **制作动图** (GIF) - 制作简单的动态图片
5. **打印输出** (TIFF) - 适合高质量打印需求

#### 视频转换分类

1. **手机播放** (MP4) - 适合手机、平板等设备播放
2. **网页播放** (WEBM) - 适合网页嵌入，加载更快
3. **电脑播放** (AVI) - 适合电脑播放器播放
4. **高清保存** (MOV) - 保持视频高清质量
5. **直播推流** (FLV) - 适合直播平台使用

#### 音频转换分类

1. **手机音乐** (MP3) - 适合手机播放，文件较小
2. **高音质** (FLAC) - 发烧友级别的音质体验
3. **电脑播放** (WAV) - 电脑播放，音质清晰
4. **开源格式** (OGG) - 开放标准，兼容性好

## 组件和接口

### 卡片数据结构

```typescript
interface ConversionCard {
  id: string
  name: string // 通俗易懂的名称
  icon: React.ReactNode // 直观的图标
  color: string
  bgColor: string
  description: string // 日常语言描述
  detailedDesc: string // 更详细的说明
  fromFormat: string
  toFormat: string
  fileType: FileType
  useCase: string // 使用场景标签
}
```

### 图标映射

```typescript
const ICON_MAPPING = {
  // 图片转换图标
  compress: <Minimize2 />,      // 压缩文件
  share: <Share2 />,            // 社交分享
  hd: <Eye />,                  // 高清保存
  gif: <Zap />,                 // 制作动图
  print: <Printer />,           // 打印输出

  // 视频转换图标
  mobile: <Smartphone />,       // 手机播放
  web: <Globe />,               // 网页播放
  computer: <Monitor />,        // 电脑播放
  quality: <Star />,            // 高清保存
  stream: <Radio />,            // 直播推流

  // 音频转换图标
  music: <Music />,             // 手机音乐
  audiophile: <Headphones />,   // 高音质
  speaker: <Volume2 />,         // 电脑播放
  opensource: <Code />          // 开源格式
}
```

## 数据模型

### 转换选项数据模型

```typescript
// 图片转换选项
const imageConversions = [
  {
    id: 'image-compress',
    name: '压缩文件',
    icon: <Minimize2 className="w-6 h-6" />,
    description: '减小文件大小，节省空间',
    detailedDesc: '将图片转换为JPG格式，大幅减小文件大小，适合存储和传输',
    toFormat: 'jpg',
    useCase: 'storage'
  },
  {
    id: 'image-share',
    name: '社交分享',
    icon: <Share2 className="w-6 h-6" />,
    description: '适合微信、QQ等平台分享',
    detailedDesc: '转换为WEBP格式，在保持质量的同时减小文件大小，适合社交媒体分享',
    toFormat: 'webp',
    useCase: 'social'
  },
  {
    id: 'image-hd',
    name: '高清保存',
    icon: <Eye className="w-6 h-6" />,
    description: '保持图片清晰度，无损压缩',
    detailedDesc: '转换为PNG格式，保持图片的所有细节和透明度',
    toFormat: 'png',
    useCase: 'quality'
  },
  {
    id: 'image-gif',
    name: '制作动图',
    icon: <Zap className="w-6 h-6" />,
    description: '制作简单的动态图片',
    detailedDesc: '转换为GIF格式，适合制作简单的动画效果',
    toFormat: 'gif',
    useCase: 'animation'
  },
  {
    id: 'image-print',
    name: '打印输出',
    icon: <Printer className="w-6 h-6" />,
    description: '适合高质量打印需求',
    detailedDesc: '转换为TIFF格式，保持最高的图片质量，适合专业打印',
    toFormat: 'tiff',
    useCase: 'print'
  }
]
```

## 错误处理

### 用户理解错误处理

1. **术语困惑处理**
   - 提供悬停提示解释专业术语
   - 在描述中使用类比和实例

2. **选择困难处理**
   - 提供"推荐"标签标识最常用选项
   - 添加"不确定？试试这个"的引导

3. **预期不符处理**
   - 在转换前显示预期结果预览
   - 提供"撤销"功能

## 测试策略

### 用户理解度测试

1. **A/B测试**
   - 对比新旧命名方案的用户选择准确率
   - 测量用户从看到选项到做出选择的时间

2. **用户访谈**
   - 询问用户对各选项名称的理解
   - 收集用户的实际使用场景

3. **可用性测试**
   - 观察用户在不同场景下的选择行为
   - 记录用户的困惑点和错误选择

### 技术测试

1. **功能测试**
   - 确保所有转换选项正常工作
   - 验证图标和描述的正确显示

2. **响应式测试**
   - 测试在不同屏幕尺寸下的显示效果
   - 确保长文本描述的正确换行

## 实现细节

### 渐进式改进策略

1. **第一阶段：更新命名**
   - 替换所有技术术语为日常用语
   - 更新图标为更直观的选择

2. **第二阶段：增强描述**
   - 添加详细的使用场景说明
   - 提供悬停提示和帮助信息

3. **第三阶段：智能推荐**
   - 根据用户历史选择推荐选项
   - 添加"最受欢迎"标签

### 国际化考虑

- 所有文本都应支持多语言
- 图标选择应考虑文化差异
- 使用场景描述应适应不同地区的使用习惯
