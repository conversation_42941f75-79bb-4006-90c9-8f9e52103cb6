import React, { useEffect, useState } from 'react'
import { IPCService } from '../services/ipcService'
import { useTheme } from '../contexts/ThemeContext'
import type { AppInfo } from '../../../shared/types'
import Logo from '../assets/logo.png'
import CustomerService from './CustomerService'
import vipIcon from '@renderer/assets/icon/vip.png';
import avatarIcon from '@renderer/assets/icon/avatar.png';
import kaiVipIcon from '@renderer/assets/icon/kaivip.png';
import { trackLogin, trackVipPurchase } from '@renderer/services/analytics-service'
import { message, Modal } from 'antd'
import { NoLoginIcon } from './Nologin'
import WxLoginModal from './WxLoginModal'
import VipPurchaseModal from './VipPurchaseModal'
import TrialService from '../services/trial-service'

interface CustomTitleBarProps { }

const CustomTitleBar: React.FC<CustomTitleBarProps> = () => {
  const { theme } = useTheme()
  const [appInfo, setAppInfo] = useState<AppInfo>({ version: '', name: '' })
  const [showLoginModal, setShowLoginModal] = useState<boolean>(false);
  const [showVipModal, setShowVipModal] = useState<boolean>(false);
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);
  const [userInfo, setUserInfo] = useState<any>(null);

  useEffect(() => {
    // Get app version info
    IPCService.getAppVersion().then(setAppInfo)

    // 检查用户登录状态
    const checkLoginStatus = () => {
      const userInfoStr = localStorage.getItem('userInfo')

      if (userInfoStr) {
        try {
          const userInfo = JSON.parse(userInfoStr)
          setIsLoggedIn(true)
          setUserInfo(userInfo)

          // 同步VIP状态到试用服务
          const trialService = TrialService.getInstance()
          trialService.syncUserVipStatus()
        } catch (error) {
          console.error('Failed to parse user info:', error)
          setIsLoggedIn(false)
          setUserInfo(null)
        }
      } else {
        setIsLoggedIn(false)
        setUserInfo(null)
      }
    }

    checkLoginStatus()

    // 监听来自ConversionPage的开通VIP事件
    const handleOpenVipEvent = () => {
      handleOpenVip()
    }

    window.addEventListener('openVipModal', handleOpenVipEvent)

    return () => {
      window.removeEventListener('openVipModal', handleOpenVipEvent)
    }
  }, [])

  const handleMinimize = () => {
    IPCService.minimizeWindow()
  }

  const handleMaximize = () => {
    IPCService.maximizeWindow()
  }

  const handleClose = () => {
    IPCService.closeWindow()
  }

  // 点击登录按钮
  const handleLoginClick = () => {
    if (isLoggedIn) {
      // 已登录，显示退出登录确认对话框
      Modal.confirm({
        title: '退出登录',
        content: '确定要退出登录吗？',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          handleLogout();
        },
        centered: true,
      });
    } else {
      // 未登录，显示登录弹窗
      trackLogin('open', 'wechat');
      setShowLoginModal(true);
    }
  };

  // 登录成功回调
  const handleLoginSuccess = async (userinfo: any) => {
    setIsLoggedIn(true);
    setUserInfo(userinfo);

    // 同步用户VIP状态到试用服务
    const trialService = TrialService.getInstance();
    trialService.syncUserVipStatus();

    // 同步 VIP 状态
    trackLogin('success', 'wechat');
    message.success(`欢迎回来，${userinfo.nickname}！`);
  };

  // 退出登录
  const handleLogout = async () => {
    localStorage.removeItem('userToken');
    localStorage.removeItem('userInfo');
    localStorage.removeItem('AUTH_TOKEN');
    setIsLoggedIn(false);
    setUserInfo(null);

    // 同步用户VIP状态到试用服务（退出后为非VIP）
    const trialService = TrialService.getInstance();
    trialService.syncUserVipStatus();

    // 退出登录后重置 VIP
    trackLogin('logout');
    message.success('已退出登录');
  };

  // 关闭登录弹窗
  const handleCloseModal = () => {
    setShowLoginModal(false);
  };

  // 处理开通 VIP
  const handleOpenVip = () => {
    // 打开 VIP 购买入口埋点
    trackVipPurchase('open');

    // 检查是否登录（使用localStorage中的用户信息）
    const userInfoStr = localStorage.getItem('userInfo')
    if (!userInfoStr) {
      message.warning('请先登录后再开通 VIP');
      setShowLoginModal(true);
      return;
    }
    setShowVipModal(true);
  };

  // 关闭 VIP 弹窗
  const handleCloseVipModal = () => {
    setShowVipModal(false);
  };

  // VIP 购买成功回调
  const handleVipPurchaseSuccess = async () => {
    // 重新获取用户信息以更新 VIP 状态
    const token = localStorage.getItem('userToken');
    if (token) {
      // 从本地读取刷新后的 userInfo 并同步 VIP 状态
      try {
        const raw = localStorage.getItem('userInfo');
        const info = raw ? JSON.parse(raw) : null;
      } catch {
      }
      // 可根据需要刷新 UI
      window.location.reload();
    }
  };

  // Theme-specific classes
  const backgroundClasses = theme === 'dark'
    ? 'bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 border-gray-700'
    : 'bg-gradient-to-r from-gray-100 via-gray-50 to-gray-100 border-gray-300'

  const buttonClasses = theme === 'dark'
    ? 'text-gray-300 hover:text-white hover:bg-gray-700 hover:bg-opacity-80'
    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-200 hover:bg-opacity-80'

  return (
    <div className={`flex justify-between items-center h-[44px] ${backgroundClasses} drag-region select-none border-b shadow-lg transition-all duration-500`}>
      <div className="flex items-center px-4">
        <div className="w-5 h-5 mr-2 flex items-center justify-center shadow-glow-sm">
          <img className='w-full h-full' src={Logo} alt="" />
        </div>
        <span className="text-white text-sm font-medium bg-gradient-to-r from-blue-200 to-purple-200 bg-clip-text text-transparent">
          格式转换大师
        </span>
        <div className="ml-2 w-1 h-1 bg-green-400 rounded-full"></div>
      </div>
      <div className="flex items-center">
        <div className='mr-[100px] flex items-center no-drag'>
          <div
            className='flex items-center gap-x-3 cursor-pointer px-3 py-2 rounded-md transition-colors'
            onClick={handleLoginClick}
          >
            {isLoggedIn ? (
              <>
                {/* 用户头像 */}
                <div className="relative">
                  <img
                    src={avatarIcon}
                    alt="avatar"
                    className="w-8 h-8 rounded-full"
                  />
                  {/* VIP 徽章 */}
                  {userInfo?.is_vip === 1 && (
                    <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-yellow-500 rounded-full flex items-center justify-center">
                      <img src={vipIcon} alt="vip勋章" />
                    </div>
                  )}
                </div>

                {/* 用户信息 */}
                <div className="flex flex-col jusctify-center items-center">
                  <span className='text-sm font-medium text-[rgba(51, 51, 51, 1)]'>
                    {userInfo?.nickname || '已登录'}
                    {userInfo?.is_vip === 1 && (
                      <span className="text-yellow-600 ml-1">(至尊会员)</span>
                    )}
                  </span>
                  {/* VIP 剩余天数 */}
                  {userInfo?.is_vip === 1 && userInfo?.vip && userInfo.vip.over_day > 0 && (
                    <span className='text-xs text-gray-500 scale-90'>
                      您的会员{userInfo.vip.over_day}天后过期
                    </span>
                  )}
                </div>
              </>
            ) : (
              <>
                <NoLoginIcon />
                <span className='text-sm text-[rgba(102, 102, 102, 1)]'>登录</span>
              </>
            )}
          </div>
          <div className='flex items-center ml-2 cursor-pointer no-drag' onClick={handleOpenVip}>
            <img src={kaiVipIcon} alt="开通vip" className='w-6 h-6 ml-2' />
            <span className='text-sm text-[#EF9749]'>开通vip</span>
          </div>
        </div>
        {/* 客服组件 */}
        <CustomerService theme={theme} />

        <button
          onClick={handleMinimize}
          className={`window-control-btn w-12 h-8 flex items-center justify-center ${buttonClasses} transition-all duration-300 hover:scale-110 group`}
          title="最小化"
        >
          <span className="text-lg leading-none group-hover:scale-125 transition-transform duration-200">−</span>
        </button>
        <button
          onClick={handleMaximize}
          className={`window-control-btn w-12 h-8 flex items-center justify-center ${buttonClasses} transition-all duration-300 hover:scale-110 group`}
          title="最大化/还原"
        >
          <span className="text-sm leading-none group-hover:scale-125 transition-transform duration-200">□</span>
        </button>
        <button
          onClick={handleClose}
          className={`window-control-btn w-12 h-8 flex items-center justify-center ${theme === 'dark' ? 'text-gray-300 hover:text-white' : 'text-gray-600 hover:text-white'} hover:bg-red-600 hover:bg-opacity-90 transition-all duration-300 hover:scale-110 group`}
          title="关闭"
        >
          <span className="text-lg leading-none group-hover:scale-125 transition-transform duration-200">×</span>
        </button>
      </div>

      {/* 微信登录弹窗 */}
      <WxLoginModal
        visible={showLoginModal}
        onCancel={handleCloseModal}
        onLoginSuccess={handleLoginSuccess}
      />

      {/* VIP 购买弹窗 */}
      <VipPurchaseModal
        visible={showVipModal}
        onCancel={handleCloseVipModal}
        onPurchaseSuccess={handleVipPurchaseSuccess}
      />
    </div>
  )
}

export default CustomTitleBar