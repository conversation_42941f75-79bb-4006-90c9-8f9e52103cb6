import sharp from 'sharp'
import * as path from 'path'
import * as fs from 'fs/promises'
import * as bmp from 'bmp-js'
import { PathUtils } from '../utils/pathUtils'
import { ProgressManager } from '../utils/progressManager'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../utils/errorHandler'
import { ConversionError, ErrorFactory, ErrorType } from '../../shared/errors'
import { HeicConverter } from './heicConverter'
import type { ConversionOptions } from '../../shared/types'

export class ImageConverter {
  private static initialized = false

  static async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      // Test Sharp availability by checking if the module loads correctly
      // Create a minimal 1x1 pixel PNG buffer for testing
      const testBuffer = Buffer.from([
        0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d, 0x49, 0x48, 0x44,
        0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x08, 0x06, 0x00, 0x00, 0x00, 0x1f,
        0x15, 0xc4, 0x89, 0x00, 0x00, 0x00, 0x0a, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9c, 0x63, 0x00,
        0x01, 0x00, 0x00, 0x05, 0x00, 0x01, 0x0d, 0x0a, 0x2d, 0xb4, 0x00, 0x00, 0x00, 0x00, 0x49,
        0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
      ])

      await sharp(testBuffer).metadata()
      this.initialized = true
      console.log('ImageConverter initialized with Sharp')
    } catch (error) {
      console.error('Failed to initialize ImageConverter:', error)
      const conversionError = ErrorFactory.fromNativeError(
        error as Error,
        ErrorType.INITIALIZATION_FAILED
      )
      await ErrorHandler.getInstance().handleError(conversionError)
      throw conversionError
    }
  }

  static async convert(
    inputPath: string,
    outputPath: string,
    inputFormat: string,
    outputFormat: string,
    options?: ConversionOptions,
    progressId?: string
  ): Promise<void> {
    const errorHandler = ErrorHandler.getInstance()

    // 检查初始化状态
    if (!this.initialized) {
      const error = new ConversionError(
        ErrorType.INITIALIZATION_FAILED,
        'ImageConverter not initialized',
        { details: { inputPath, outputPath } }
      )
      await errorHandler.handleError(error, { inputPath, outputPath, outputFormat })
      throw error
    }

    // 验证输入文件
    if (!PathUtils.isValidPath(inputPath)) {
      const error = ErrorFactory.fileNotFound(inputPath)
      await errorHandler.handleError(error, { outputPath, outputFormat })
      throw error
    }

    // 验证输出目录
    try {
      const outputDir = path.dirname(outputPath)
      PathUtils.ensureDirectoryExists(outputDir)
    } catch (err) {
      const error = ErrorFactory.fileAccessDenied(path.dirname(outputPath))
      await errorHandler.handleError(error, { inputPath, outputPath })
      throw error
    }

    const progressManager = ProgressManager.getInstance()

    try {
      // Check if this is a HEIC input format conversion
      const normalizedInputFormat = inputFormat.toLowerCase()

      if (normalizedInputFormat === 'heic' || normalizedInputFormat === 'heif') {
        // Use HeicConverter for HEIC input conversions
        await HeicConverter.convert(inputPath, outputPath, inputFormat, outputFormat, options, progressId)
        return
      }

      // Start progress tracking
      if (progressId) {
        progressManager.startConversion(progressId)
        progressManager.updateProgress(progressId, { progress: 10 })
      }

      // Create Sharp instance
      let sharpInstance = sharp(inputPath)

      // Get input image metadata
      const metadata = await sharpInstance.metadata()

      if (progressId) {
        progressManager.updateProgress(progressId, {
          progress: 20,
          size: ProgressManager.formatFileSize(metadata.size || 0)
        })
      }

      // Apply image options with validation
      if (options?.width || options?.height) {
        const resizeOptions: sharp.ResizeOptions = {}

        if (options.width && options.width > 0) {
          resizeOptions.width = Math.round(options.width)
        }

        if (options.height && options.height > 0) {
          resizeOptions.height = Math.round(options.height)
        }

        // Maintain aspect ratio by default
        resizeOptions.fit = 'inside'
        resizeOptions.withoutEnlargement = true

        sharpInstance = sharpInstance.resize(resizeOptions)
      }

      if (progressId) {
        progressManager.updateProgress(progressId, { progress: 40 })
      }

      // Apply format-specific options
      const normalizedOutputFormat = outputFormat.toLowerCase()

      let outputIsBmp = false
      switch (normalizedOutputFormat) {
        case 'jpg':
        case 'jpeg':
          const jpegOptions: sharp.JpegOptions = {}
          if (options?.quality && options.quality >= 1 && options.quality <= 100) {
            jpegOptions.quality = Math.round(options.quality)
          }
          sharpInstance = sharpInstance.jpeg(jpegOptions)
          break

        case 'png':
          const pngOptions: sharp.PngOptions = {}
          if (
            options?.compressionLevel &&
            options.compressionLevel >= 0 &&
            options.compressionLevel <= 9
          ) {
            pngOptions.compressionLevel = Math.round(options.compressionLevel)
          }
          if (options?.quality && options.quality >= 1 && options.quality <= 100) {
            pngOptions.quality = Math.round(options.quality)
          }
          sharpInstance = sharpInstance.png(pngOptions)
          break

        case 'webp':
          const webpOptions: sharp.WebpOptions = {}
          if (options?.quality && options.quality >= 1 && options.quality <= 100) {
            webpOptions.quality = Math.round(options.quality)
          }
          sharpInstance = sharpInstance.webp(webpOptions)
          break

        case 'tiff':
          const tiffOptions: sharp.TiffOptions = {}
          if (options?.quality && options.quality >= 1 && options.quality <= 100) {
            tiffOptions.quality = Math.round(options.quality)
          }
          sharpInstance = sharpInstance.tiff(tiffOptions)
          break

        case 'gif':
          // Sharp doesn't support GIF output directly, convert to PNG instead
          if (normalizedOutputFormat === 'gif') {
            console.warn('GIF output not supported by Sharp, converting to PNG instead')
            sharpInstance = sharpInstance.png()
          }
          break

        case 'bmp':
          // bmp handled separately using raw -> bmp-js encode
          outputIsBmp = true
          break

        default:
          const error = ErrorFactory.unsupportedFormat('image', outputFormat)
          await errorHandler.handleError(error, { inputPath, outputPath, outputFormat })
          throw error
      }

      if (progressId) {
        progressManager.updateProgress(progressId, { progress: 60 })
      }

      if (outputIsBmp) {
        // Ensure 4 channels (RGBA) for bmp-js
        const rawResult = await sharpInstance
          .toColourspace('srgb')
          .ensureAlpha()
          .raw()
          .toBuffer({ resolveWithObject: true })

        const { data, info } = rawResult

        if (progressId) {
          progressManager.updateProgress(progressId, { progress: 80 })
        }

        // Ensure RGBA buffer
        let rgbaData = data
        if (info.channels !== 4) {
          const converted = await sharp(data, {
            raw: { width: info.width || 0, height: info.height || 0, channels: info.channels || 3 }
          })
            .ensureAlpha()
            .raw()
            .toBuffer()
          rgbaData = converted
        }

        // Convert RGBA -> ABGR format as required by bmp-js
        // bmp-js expects data in ABGR format: Alpha-Blue-Green-Red
        const w = info.width || 0
        const h = info.height || 0
        const abgr = Buffer.from(rgbaData) // copy
        for (let i = 0; i < abgr.length; i += 4) {
          const r = rgbaData[i]
          const g = rgbaData[i + 1]
          const b = rgbaData[i + 2]
          const a = rgbaData[i + 3]
          // Convert RGBA to ABGR: A-B-G-R
          abgr[i] = a     // Alpha
          abgr[i + 1] = b // Blue
          abgr[i + 2] = g // Green
          abgr[i + 3] = r // Red
        }

        const encoded = bmp.encode({ data: abgr, width: w, height: h })
        await fs.writeFile(outputPath, encoded.data)
      } else {
        // Perform the conversion via Sharp
        const outputBuffer = await sharpInstance.toBuffer()

        if (progressId) {
          progressManager.updateProgress(progressId, { progress: 80 })
        }

        // Write to output file
        await sharp(outputBuffer).toFile(outputPath)
      }

      if (progressId) {
        progressManager.updateProgress(progressId, { progress: 100 })
        progressManager.completeConversion(progressId)
      }

      console.log(`Image conversion completed: ${inputPath} -> ${outputPath}`)
    } catch (error) {
      console.error('Image conversion error:', error)

      // Create detailed conversion error
      const conversionError = ErrorFactory.conversionFailed((error as Error).message, {
        inputPath,
        outputPath,
        outputFormat,
        options,
        sharpError: error
      })

      // Handle the error
      await errorHandler.handleError(conversionError, {
        converter: 'ImageConverter',
        inputPath,
        outputPath,
        options
      })

      if (progressId) {
        progressManager.errorConversion(progressId, conversionError.message)
      }

      throw conversionError
    }
  }

  static getSupportedFormats(): { input: string[]; output: string[] } {
    return {
      input: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff', 'svg', 'ico', 'heic', 'heif'],
      output: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'tiff', 'bmp']
    }
  }

  static async getImageInfo(inputPath: string): Promise<sharp.Metadata> {
    if (!this.initialized) {
      throw new Error('ImageConverter not initialized')
    }

    try {
      const metadata = await sharp(inputPath).metadata()
      return metadata
    } catch (error) {
      throw new Error(`Failed to get image info: ${(error as Error).message}`)
    }
  }

  static async optimizeImage(
    inputPath: string,
    outputPath: string,
    quality: number = 80
  ): Promise<void> {
    if (!this.initialized) {
      throw new Error('ImageConverter not initialized')
    }

    try {
      const metadata = await sharp(inputPath).metadata()
      const format = metadata.format

      let sharpInstance = sharp(inputPath)

      switch (format) {
        case 'jpeg':
          sharpInstance = sharpInstance.jpeg({ quality, progressive: true })
          break
        case 'png':
          sharpInstance = sharpInstance.png({ quality, progressive: true })
          break
        case 'webp':
          sharpInstance = sharpInstance.webp({ quality })
          break
        default:
          // For other formats, convert to JPEG with optimization
          sharpInstance = sharpInstance.jpeg({ quality, progressive: true })
      }

      await sharpInstance.toFile(outputPath)
    } catch (error) {
      throw new Error(`Failed to optimize image: ${(error as Error).message}`)
    }
  }

  static async resizeImage(
    inputPath: string,
    outputPath: string,
    width?: number,
    height?: number,
    maintainAspectRatio: boolean = true
  ): Promise<void> {
    if (!this.initialized) {
      throw new Error('ImageConverter not initialized')
    }

    try {
      const resizeOptions: sharp.ResizeOptions = {
        fit: maintainAspectRatio ? 'inside' : 'fill',
        withoutEnlargement: true
      }

      if (width && width > 0) {
        resizeOptions.width = Math.round(width)
      }

      if (height && height > 0) {
        resizeOptions.height = Math.round(height)
      }

      await sharp(inputPath).resize(resizeOptions).toFile(outputPath)
    } catch (error) {
      throw new Error(`Failed to resize image: ${(error as Error).message}`)
    }
  }
}
