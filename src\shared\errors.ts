// 错误类型定义
export enum ErrorType {
  // 文件相关错误
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  FILE_ACCESS_DENIED = 'FILE_ACCESS_DENIED',
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  INVALID_FILE_FORMAT = 'INVALID_FILE_FORMAT',
  
  // 转换相关错误
  CONVERSION_FAILED = 'CONVERSION_FAILED',
  FFMPEG_NOT_FOUND = 'FFMPEG_NOT_FOUND',
  SHARP_NOT_AVAILABLE = 'SHARP_NOT_AVAILABLE',
  UNSUPPORTED_FORMAT = 'UNSUPPORTED_FORMAT',
  INVALID_CONVERSION_OPTIONS = 'INVALID_CONVERSION_OPTIONS',
  
  // 系统相关错误
  INSUFFICIENT_DISK_SPACE = 'INSUFFICIENT_DISK_SPACE',
  NETWORK_ERROR = 'NETWORK_ERROR',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  
  // 应用相关错误
  INITIALIZATION_FAILED = 'INITIALIZATION_FAILED',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// 错误严重程度
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// 自定义错误类
export class ConversionError extends Error {
  public readonly type: ErrorType
  public readonly severity: ErrorSeverity
  public readonly code: string
  public readonly details?: any
  public readonly timestamp: Date
  public readonly recoverable: boolean

  constructor(
    type: ErrorType,
    message: string,
    options: {
      severity?: ErrorSeverity
      code?: string
      details?: any
      recoverable?: boolean
      cause?: Error
    } = {}
  ) {
    super(message)
    this.name = 'ConversionError'
    this.type = type
    this.severity = options.severity || ErrorSeverity.MEDIUM
    this.code = options.code || type
    this.details = options.details
    this.timestamp = new Date()
    this.recoverable = options.recoverable ?? true
    
    if (options.cause) {
      this.cause = options.cause
    }
  }

  // 转换为可序列化的对象
  toJSON() {
    return {
      name: this.name,
      type: this.type,
      message: this.message,
      severity: this.severity,
      code: this.code,
      details: this.details,
      timestamp: this.timestamp.toISOString(),
      recoverable: this.recoverable,
      stack: this.stack
    }
  }

  // 从序列化对象创建错误实例
  static fromJSON(data: any): ConversionError {
    const error = new ConversionError(data.type, data.message, {
      severity: data.severity,
      code: data.code,
      details: data.details,
      recoverable: data.recoverable
    })
    error.stack = data.stack
    return error
  }
}

// 错误消息映射
export const ERROR_MESSAGES: Record<ErrorType, string> = {
  [ErrorType.FILE_NOT_FOUND]: '找不到指定的文件',
  [ErrorType.FILE_ACCESS_DENIED]: '无法访问文件，请检查文件权限',
  [ErrorType.FILE_TOO_LARGE]: '文件过大，请选择较小的文件',
  [ErrorType.INVALID_FILE_FORMAT]: '不支持的文件格式',
  
  [ErrorType.CONVERSION_FAILED]: '文件转换失败',
  [ErrorType.FFMPEG_NOT_FOUND]: 'FFmpeg 未安装或无法找到',
  [ErrorType.SHARP_NOT_AVAILABLE]: 'Sharp 图片处理库不可用',
  [ErrorType.UNSUPPORTED_FORMAT]: '不支持的转换格式',
  [ErrorType.INVALID_CONVERSION_OPTIONS]: '转换参数无效',
  
  [ErrorType.INSUFFICIENT_DISK_SPACE]: '磁盘空间不足',
  [ErrorType.NETWORK_ERROR]: '网络连接错误',
  [ErrorType.PERMISSION_DENIED]: '权限不足',
  
  [ErrorType.INITIALIZATION_FAILED]: '应用初始化失败',
  [ErrorType.UNKNOWN_ERROR]: '未知错误'
}

// 用户友好的错误消息
export const USER_FRIENDLY_MESSAGES: Record<ErrorType, string> = {
  [ErrorType.FILE_NOT_FOUND]: '文件不存在或已被移动，请重新选择文件',
  [ErrorType.FILE_ACCESS_DENIED]: '无法读取文件，请确保文件未被其他程序占用',
  [ErrorType.FILE_TOO_LARGE]: '文件太大了！请选择小于 500MB 的文件',
  [ErrorType.INVALID_FILE_FORMAT]: '这个文件格式不支持，请选择其他文件',
  
  [ErrorType.CONVERSION_FAILED]: '转换过程中出现问题，请稍后重试',
  [ErrorType.FFMPEG_NOT_FOUND]: '缺少必要的转换组件，请重新安装应用',
  [ErrorType.SHARP_NOT_AVAILABLE]: '图片处理组件不可用，请重新安装应用',
  [ErrorType.UNSUPPORTED_FORMAT]: '暂不支持这种格式转换',
  [ErrorType.INVALID_CONVERSION_OPTIONS]: '转换设置有误，请检查参数',
  
  [ErrorType.INSUFFICIENT_DISK_SPACE]: '磁盘空间不足，请清理磁盘后重试',
  [ErrorType.NETWORK_ERROR]: '网络连接异常，请检查网络设置',
  [ErrorType.PERMISSION_DENIED]: '没有足够的权限执行此操作',
  
  [ErrorType.INITIALIZATION_FAILED]: '应用启动失败，请重新启动应用',
  [ErrorType.UNKNOWN_ERROR]: '出现了意外错误，请稍后重试'
}

// 错误解决建议
export const ERROR_SOLUTIONS: Record<ErrorType, string[]> = {
  [ErrorType.FILE_NOT_FOUND]: [
    '检查文件是否存在',
    '重新选择文件',
    '确保文件路径正确'
  ],
  [ErrorType.FILE_ACCESS_DENIED]: [
    '关闭正在使用该文件的其他程序',
    '以管理员身份运行应用',
    '检查文件权限设置'
  ],
  [ErrorType.FILE_TOO_LARGE]: [
    '选择较小的文件（建议小于 500MB）',
    '使用其他工具预先压缩文件',
    '分段处理大文件'
  ],
  [ErrorType.INVALID_FILE_FORMAT]: [
    '检查文件扩展名是否正确',
    '使用支持的文件格式',
    '尝试重新保存文件'
  ],
  
  [ErrorType.CONVERSION_FAILED]: [
    '检查输入文件是否完整',
    '尝试使用不同的转换参数',
    '重新启动应用后重试'
  ],
  [ErrorType.FFMPEG_NOT_FOUND]: [
    '重新安装应用',
    '检查系统是否安装了 FFmpeg',
    '联系技术支持'
  ],
  [ErrorType.SHARP_NOT_AVAILABLE]: [
    '重新安装应用',
    '检查系统兼容性',
    '联系技术支持'
  ],
  [ErrorType.UNSUPPORTED_FORMAT]: [
    '查看支持的格式列表',
    '使用其他转换工具',
    '等待后续版本支持'
  ],
  [ErrorType.INVALID_CONVERSION_OPTIONS]: [
    '重置转换参数为默认值',
    '检查参数范围是否正确',
    '参考帮助文档'
  ],
  
  [ErrorType.INSUFFICIENT_DISK_SPACE]: [
    '清理磁盘空间',
    '选择其他输出位置',
    '删除不需要的文件'
  ],
  [ErrorType.NETWORK_ERROR]: [
    '检查网络连接',
    '重新连接网络',
    '联系网络管理员'
  ],
  [ErrorType.PERMISSION_DENIED]: [
    '以管理员身份运行',
    '检查文件夹权限',
    '选择其他输出位置'
  ],
  
  [ErrorType.INITIALIZATION_FAILED]: [
    '重新启动应用',
    '检查系统兼容性',
    '重新安装应用'
  ],
  [ErrorType.UNKNOWN_ERROR]: [
    '重新启动应用',
    '检查系统日志',
    '联系技术支持'
  ]
}

// 错误工厂函数
export class ErrorFactory {
  static fileNotFound(filePath: string): ConversionError {
    return new ConversionError(ErrorType.FILE_NOT_FOUND, ERROR_MESSAGES[ErrorType.FILE_NOT_FOUND], {
      severity: ErrorSeverity.MEDIUM,
      details: { filePath },
      recoverable: true
    })
  }

  static fileAccessDenied(filePath: string): ConversionError {
    return new ConversionError(ErrorType.FILE_ACCESS_DENIED, ERROR_MESSAGES[ErrorType.FILE_ACCESS_DENIED], {
      severity: ErrorSeverity.MEDIUM,
      details: { filePath },
      recoverable: true
    })
  }

  static fileTooLarge(filePath: string, size: number, maxSize: number): ConversionError {
    return new ConversionError(ErrorType.FILE_TOO_LARGE, ERROR_MESSAGES[ErrorType.FILE_TOO_LARGE], {
      severity: ErrorSeverity.LOW,
      details: { filePath, size, maxSize },
      recoverable: true
    })
  }

  static invalidFileFormat(filePath: string, format: string): ConversionError {
    return new ConversionError(ErrorType.INVALID_FILE_FORMAT, ERROR_MESSAGES[ErrorType.INVALID_FILE_FORMAT], {
      severity: ErrorSeverity.LOW,
      details: { filePath, format },
      recoverable: true
    })
  }

  static conversionFailed(reason: string, details?: any): ConversionError {
    return new ConversionError(ErrorType.CONVERSION_FAILED, ERROR_MESSAGES[ErrorType.CONVERSION_FAILED], {
      severity: ErrorSeverity.HIGH,
      details: { reason, ...details },
      recoverable: true
    })
  }

  static ffmpegNotFound(): ConversionError {
    return new ConversionError(ErrorType.FFMPEG_NOT_FOUND, ERROR_MESSAGES[ErrorType.FFMPEG_NOT_FOUND], {
      severity: ErrorSeverity.CRITICAL,
      recoverable: false
    })
  }

  static sharpNotAvailable(): ConversionError {
    return new ConversionError(ErrorType.SHARP_NOT_AVAILABLE, ERROR_MESSAGES[ErrorType.SHARP_NOT_AVAILABLE], {
      severity: ErrorSeverity.CRITICAL,
      recoverable: false
    })
  }

  static unsupportedFormat(inputFormat: string, outputFormat: string): ConversionError {
    return new ConversionError(ErrorType.UNSUPPORTED_FORMAT, ERROR_MESSAGES[ErrorType.UNSUPPORTED_FORMAT], {
      severity: ErrorSeverity.MEDIUM,
      details: { inputFormat, outputFormat },
      recoverable: true
    })
  }

  static fromNativeError(error: Error, type: ErrorType = ErrorType.UNKNOWN_ERROR): ConversionError {
    return new ConversionError(type, error.message, {
      severity: ErrorSeverity.MEDIUM,
      cause: error,
      recoverable: true
    })
  }
}