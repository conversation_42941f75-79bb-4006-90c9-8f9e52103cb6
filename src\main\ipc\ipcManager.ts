import { ipc<PERSON><PERSON>, BrowserWindow, app, dialog } from 'electron'
import * as os from 'os'
import { IPC_CHANNELS } from '../../shared/constants'
import type { AppInfo, AppSettings } from '../../shared/types'

export class IPCManager {
  private static conversionService: any
  private static settingsService: any

  static async initialize() {
    // Import services
    const { ConversionService } = await import('../services/conversionService')
    const { SettingsService } = await import('../services/settingsService')
    
    this.conversionService = ConversionService
    this.settingsService = SettingsService

    // Initialize conversion service (FFmpeg setup)
    try {
      await ConversionService.initialize()
    } catch (error) {
      console.error('Failed to initialize ConversionService:', error)
    }

    this.setupWindowHandlers()
    this.setupAppHandlers()
    this.setupConversionHandlers()
    this.setupSettingsHandlers()
    this.setupFileHandlers()
    this.setupSystemHandlers()
    this.setupImageHandlers()
  }

  private static setupWindowHandlers() {
    // Window control handlers
    ipcMain.handle(IPC_CHANNELS.WINDOW_MINIMIZE, (event) => {
      const window = BrowserWindow.fromWebContents(event.sender)
      window?.minimize()
    })

    ipcMain.handle(IPC_CHANNELS.WINDOW_MAXIMIZE, (event) => {
      const window = BrowserWindow.fromWebContents(event.sender)
      if (window?.isMaximized()) {
        window.unmaximize()
      } else {
        window?.maximize()
      }
    })

    ipcMain.handle(IPC_CHANNELS.WINDOW_CLOSE, (event) => {
      const window = BrowserWindow.fromWebContents(event.sender)
      window?.close()
    })
  }

  private static setupAppHandlers() {
    // App version handler
    ipcMain.handle(IPC_CHANNELS.GET_APP_VERSION, (): AppInfo => {
      return {
        version: app.getVersion(),
        name: app.getName()
      }
    })
  }

  private static setupConversionHandlers() {
    // File conversion handlers
    ipcMain.handle(IPC_CHANNELS.CONVERT_FILE, async (_event, inputPath, outputPath, inputFormat, outputFormat, fileType, options) => {
      try {
        const task = await this.conversionService.convertFile(
          inputPath,
          outputPath,
          inputFormat,
          outputFormat,
          fileType,
          options
        )
        return task
      } catch (error) {
        console.error('Conversion failed:', error)
        throw error
      }
    })

    ipcMain.handle(IPC_CHANNELS.GET_CONVERSION_PROGRESS, async (_event, taskId) => {
      try {
        return await this.conversionService.getConversionProgress(taskId)
      } catch (error) {
        console.error('Failed to get conversion progress:', error)
        return 0
      }
    })

    ipcMain.handle(IPC_CHANNELS.CANCEL_CONVERSION, async (_event, taskId) => {
      try {
        await this.conversionService.cancelConversion(taskId)
      } catch (error) {
        console.error('Failed to cancel conversion:', error)
        throw error
      }
    })

    ipcMain.handle(IPC_CHANNELS.GET_SUPPORTED_FORMATS, async () => {
      try {
        const { FILE_TYPES } = await import('../../shared/constants')
        return FILE_TYPES
      } catch (error) {
        console.error('Failed to get supported formats:', error)
        throw error
      }
    })

    // Progress management handlers
    ipcMain.handle('get-all-conversions', async () => {
      try {
        const { ProgressManager } = await import('../utils/progressManager')
        return ProgressManager.getInstance().getAllConversions()
      } catch (error) {
        console.error('Failed to get all conversions:', error)
        throw error
      }
    })

    ipcMain.handle('get-active-conversions', async () => {
      try {
        const { ProgressManager } = await import('../utils/progressManager')
        return ProgressManager.getInstance().getActiveConversions()
      } catch (error) {
        console.error('Failed to get active conversions:', error)
        throw error
      }
    })

    ipcMain.handle('clear-completed-conversions', async () => {
      try {
        const { ProgressManager } = await import('../utils/progressManager')
        ProgressManager.getInstance().clearCompleted()
      } catch (error) {
        console.error('Failed to clear completed conversions:', error)
        throw error
      }
    })

    ipcMain.handle('remove-conversion', async (_event, conversionId: string) => {
      try {
        const { ProgressManager } = await import('../utils/progressManager')
        ProgressManager.getInstance().removeConversion(conversionId)
      } catch (error) {
        console.error('Failed to remove conversion:', error)
        throw error
      }
    })
  }

  private static setupSettingsHandlers() {
    // Settings handlers
    ipcMain.handle(IPC_CHANNELS.GET_SETTINGS, async () => {
      try {
        return await this.settingsService.getSettings()
      } catch (error) {
        console.error('Failed to get settings:', error)
        // Return default settings on error
        return {
          outputPath: require('path').join(require('os').homedir(), 'Desktop', '格式转换'),
          theme: 'dark' as const,
          autoOpenOutput: true,
          maxConcurrentTasks: 3
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.UPDATE_SETTINGS, async (_event, settings: Partial<AppSettings>) => {
      try {
        await this.settingsService.updateSettings(settings)
      } catch (error) {
        console.error('Failed to update settings:', error)
        throw error
      }
    })
  }

  private static setupFileHandlers() {
    // File system handlers
    ipcMain.handle(IPC_CHANNELS.OPEN_OUTPUT_FOLDER, async (_event, folderPath?: string) => {
      try {
        await this.conversionService.openOutputFolder(folderPath)
      } catch (error) {
        console.error('Failed to open output folder:', error)
        throw error
      }
    })

    // Open directory handler
    ipcMain.handle(IPC_CHANNELS.OPEN_DIRECTORY, async (_event, outputPath: string) => {
      try {
        const { shell } = await import('electron')
        const fs = await import('fs')
        const path = await import('path')

        if (!outputPath) {
          return { success: false, error: '输出路径为空' }
        }

        console.log('尝试打开目录:', outputPath)

        // 确保路径存在
        if (!fs.existsSync(outputPath)) {
          // 如果文件路径不存在，尝试打开文件所在的目录
          const dirPath = path.dirname(outputPath)
          if (fs.existsSync(dirPath)) {
            await shell.openPath(dirPath)
            return { success: true }
          }
          return { success: false, error: '目录不存在' }
        }

        // 如果是文件，打开文件所在目录并选中文件
        const stats = fs.statSync(outputPath)
        if (stats.isFile()) {
          await shell.showItemInFolder(outputPath)
        } else {
          await shell.openPath(outputPath)
        }

        return { success: true }
      } catch (error: any) {
        console.error('打开目录失败:', error)
        return { success: false, error: error.message }
      }
    })

    // File dialog handler
    ipcMain.handle(IPC_CHANNELS.SHOW_OPEN_DIALOG, async (_event, options) => {
      try {
        const result = await dialog.showOpenDialog(options)
        return result
      } catch (error) {
        console.error('Failed to show open dialog:', error)
        throw error
      }
    })

    // Save dialog handler
    ipcMain.handle(IPC_CHANNELS.SHOW_SAVE_DIALOG, async (_event, options) => {
      try {
        const result = await dialog.showSaveDialog(options)
        return result
      } catch (error) {
        console.error('Failed to show save dialog:', error)
        throw error
      }
    })
  }

  private static setupSystemHandlers() {
    // System info handler
    ipcMain.handle(IPC_CHANNELS.GET_SYSTEM_INFO, () => {
      try {
        return {
          platform: os.platform(),
          arch: os.arch(),
          release: os.release(),
          totalMemory: os.totalmem(),
          freeMemory: os.freemem(),
          cpus: os.cpus().length,
          homedir: os.homedir(),
          tmpdir: os.tmpdir()
        }
      } catch (error) {
        console.error('Failed to get system info:', error)
        throw error
      }
    })

    // Message box handler
    ipcMain.handle(IPC_CHANNELS.SHOW_MESSAGE_BOX, async (_event, options) => {
      try {
        const result = await dialog.showMessageBox(options)
        return result
      } catch (error) {
        console.error('Failed to show message box:', error)
        throw error
      }
    })

    // Error box handler
    ipcMain.handle(IPC_CHANNELS.SHOW_ERROR_BOX, (_event, title: string, content: string) => {
      try {
        dialog.showErrorBox(title, content)
      } catch (error) {
        console.error('Failed to show error box:', error)
        throw error
      }
    })
  }

  private static setupImageHandlers() {
    // Image-specific handlers
    ipcMain.handle(IPC_CHANNELS.GET_IMAGE_INFO, async (_event, imagePath: string) => {
      try {
        const { ImageConverter } = await import('../converters/imageConverter')
        return await ImageConverter.getImageInfo(imagePath)
      } catch (error) {
        console.error('Failed to get image info:', error)
        throw error
      }
    })

    ipcMain.handle(IPC_CHANNELS.OPTIMIZE_IMAGE, async (_event, inputPath: string, outputPath: string, quality: number) => {
      try {
        const { ImageConverter } = await import('../converters/imageConverter')
        await ImageConverter.optimizeImage(inputPath, outputPath, quality)
      } catch (error) {
        console.error('Failed to optimize image:', error)
        throw error
      }
    })

    ipcMain.handle(IPC_CHANNELS.RESIZE_IMAGE, async (_event, inputPath: string, outputPath: string, width?: number, height?: number) => {
      try {
        const { ImageConverter } = await import('../converters/imageConverter')
        await ImageConverter.resizeImage(inputPath, outputPath, width, height)
      } catch (error) {
        console.error('Failed to resize image:', error)
        throw error
      }
    })
  }

  static cleanup() {
    // Remove all IPC handlers
    Object.values(IPC_CHANNELS).forEach(channel => {
      ipcMain.removeAllListeners(channel)
    })
    
    // Remove additional handlers
    ipcMain.removeAllListeners('ping')
  }
}