import React, { useState, useCallback, useRef } from 'react'
import { ArrowLef<PERSON>, Upload, Setting<PERSON>, Play, FolderOpen } from 'lucide-react'
import { ConversionService } from '../services/conversionService'
import { IPCService } from '../services/ipcService'
import { getFileExtension, getFileType, getSupportedOutputFormats } from '../utils/fileUtils'
import { USER_CONFIG } from '../../../shared/constants'
import Notification from '../components/Notification'
import ProgressNotification from '../components/ProgressNotification'
import ConversionProgress from '../components/ConversionProgress'
import type { FileType, SupportedFormat, ConversionOptions, TrialCategory } from '../../../shared/types'
import { trackPageSaveAction } from '@renderer/services/analytics-service'
import TrialService from '../services/trial-service'
import TrialLimitModal from '../components/TrialLimitModal'

interface ConversionPageProps {
  fromFormat: string
  toFormat: string
  fileType: FileType
  onBack: () => void
}

const ConversionPage: React.FC<ConversionPageProps> = ({
  fromFormat,
  toFormat,
  fileType,
  onBack
}) => {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const fileInputRef = useRef<HTMLInputElement | null>(null)
  const [isDragActiveLocal, setIsDragActiveLocal] = useState(false)
  const [outputFormat, setOutputFormat] = useState<string>(toFormat)
  const [isConverting, setIsConverting] = useState(false)
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false)
  const [conversionOptions, setConversionOptions] = useState<ConversionOptions>({})
  const [errorMessage, setErrorMessage] = useState<string>('')
  const [rejectedFiles, setRejectedFiles] = useState<File[]>([])
  const [notification, setNotification] = useState<{
    type: 'success' | 'error' | 'warning' | 'info'
    title: string
    message?: string
  } | null>(null)
  const [showProgressDetails, setShowProgressDetails] = useState(false)
  const [trialService] = useState(() => TrialService.getInstance())
  const [showTrialModal, setShowTrialModal] = useState(false)

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    setErrorMessage('')
    console.log(acceptedFiles);
    

    // 验证文件类型
    const validFiles: File[] = []
    const invalidFiles: File[] = []

    acceptedFiles.forEach(file => {
      const extension = getFileExtension(file.name)
      const detectedFileType = getFileType(extension)

      if (detectedFileType === fileType) {
        // 检查文件大小 (限制为 500MB)
        if (file.size > 500 * 1024 * 1024) {
          invalidFiles.push(file)
        } else {
          validFiles.push(file)
        }
      } else {
        invalidFiles.push(file)
      }
    })

    // 添加被拒绝的文件
    rejectedFiles.forEach(rejection => {
      invalidFiles.push(rejection.file)
    })

    if (validFiles.length > 0) {
      setSelectedFiles(prev => [...prev, ...validFiles])
    }

    if (invalidFiles.length > 0) {
      setRejectedFiles(invalidFiles)
      setErrorMessage(`${invalidFiles.length} 个文件被拒绝：格式不支持或文件过大（限制500MB）`)
    }
  }, [fileType])

  // Custom handlers replacing react-dropzone
  const onFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    if (files.length) {
      onDrop(files, [])
    }
    // reset input to allow re-selecting same files
    e.target.value = ''
  }

  const handleClickSelect = () => {
    fileInputRef.current?.click()
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragActiveLocal(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragActiveLocal(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragActiveLocal(false)
    const files = Array.from(e.dataTransfer.files || [])
    if (files.length) {
      onDrop(files as File[], [])
    }
  }

  function getAcceptedFileTypes() {
    // 使用严格的文件扩展名匹配，而不是依赖浏览器的MIME类型判断
    const inputFormats = (() => {
      switch (fileType) {
        case 'VIDEO':
          return ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm']
        case 'AUDIO':
          return ['mp3', 'wav', 'flac', 'ogg', 'wma']
        case 'IMAGE':
          return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'tiff']
        default:
          return []
      }
    })()

    // 只使用具体的文件扩展名，不使用通用的MIME类型
    const acceptObject: Record<string, string[]> = {}
    const extensions = inputFormats.map(ext => `.${ext}`)

    // 使用一个通用的key，但只接受特定扩展名
    acceptObject['*/*'] = extensions

    return acceptObject
  }

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index))
    // 清除错误消息如果没有文件了
    if (selectedFiles.length === 1) {
      setErrorMessage('')
      setRejectedFiles([])
    }
  }

  const clearAllFiles = () => {
    setSelectedFiles([])
    setErrorMessage('')
    setRejectedFiles([])
  }

  const getSupportedInputFormats = () => {
    switch (fileType) {
      case 'VIDEO':
        return ['MP4', 'AVI', 'MOV', 'MKV', 'WMV', 'FLV', 'WEBM']
      case 'AUDIO':
        return ['MP3', 'WAV', 'FLAC', 'OGG', 'WMA']
      case 'IMAGE':
        return ['JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'TIFF']
      default:
        return []
    }
  }

  const handleConvert = async () => {
    if (selectedFiles.length === 0) return

    // 检查试用限制（试用完后自动调用开通VIP逻辑）
    const category = fileType as TrialCategory
    if (!trialService.canUseCategory(category)) {
      // 直接调用开通VIP逻辑（handleOpenVip内部已有登录判断）
      const event = new CustomEvent('openVipModal')
      window.dispatchEvent(event)
      return
    }

    setIsConverting(true)
    setErrorMessage('')

    try {
      const tasks: Promise<any>[] = []
      let firstOutputPath = ''

      for (const file of selectedFiles) {
        const inputPath = ConversionService.getPathForFile(file)

        // 生成输出路径，添加类别前缀
        const fileName = file.name.split('.').slice(0, -1).join('.')
        const categoryPrefix = getCategoryPrefix(fileType)
        const outputPath = `${categoryPrefix}${fileName}.${outputFormat}`

        // 记录第一个文件的输出路径，用于后续打开目录
        if (!firstOutputPath) {
          firstOutputPath = outputPath
        }

        const task = ConversionService.convertFile(
          inputPath,
          outputPath,
          getFileExtension(file.name) as SupportedFormat,
          outputFormat as SupportedFormat,
          fileType,
          conversionOptions
        )

        tasks.push(task)
      }

      const results = await Promise.all(tasks)

      // 转换完成，显示成功消息
      setErrorMessage('')
      setNotification({
        type: 'success',
        title: '转换完成',
        message: `成功转换 ${selectedFiles.length} 个文件`
      })

      // 标记该类别已试用（仅对非VIP用户）
      trialService.markCategoryUsed(category)

      // 转换完成后自动打开输出目录
      if (results.length > 0 && results[0].outputPath) {
        try {
          const result = await IPCService.openDirectory(results[0].outputPath)
          if (!result.success) {
            console.warn('打开输出目录失败:', result.error)
          }
        } catch (error) {
          console.warn('打开输出目录时出错:', error)
        }
      }

      trackPageSaveAction(`${getCategoryPrefix(fileType)}${toFormat}`, toFormat, 'save')

      // 清空选中的文件
      setSelectedFiles([])

    } catch (error) {
      console.error('Conversion failed:', error)
      setErrorMessage(`转换失败: ${error instanceof Error ? error.message : '未知错误'}`)
      setNotification({
        type: 'error',
        title: '转换失败',
        message: error instanceof Error ? error.message : '未知错误'
      })
    } finally {
      setIsConverting(false)
    }
  }

  const getFileTypeIcon = () => {
    switch (fileType) {
      case 'VIDEO':
        return '🎬'
      case 'AUDIO':
        return '🎵'
      case 'IMAGE':
        return '🖼️'
      default:
        return '📄'
    }
  }

  const getFileTypeColor = () => {
    switch (fileType) {
      case 'VIDEO':
        return 'from-red-500 to-pink-600'
      case 'AUDIO':
        return 'from-green-500 to-teal-600'
      case 'IMAGE':
        return 'from-blue-500 to-purple-600'
      default:
        return 'from-gray-500 to-gray-600'
    }
  }

  const getCategoryPrefix = (fileType: FileType): string => {
    switch (fileType) {
      case 'VIDEO':
        return '视频转换_'
      case 'AUDIO':
        return '音频转换_'
      case 'IMAGE':
        return '图片转换_'
      default:
        return '格式转换_'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 p-8 relative overflow-hidden">
      {/* Static Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-10"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-10"></div>
      </div>

      {/* Notification */}
      {notification && (
        <Notification
          type={notification.type}
          title={notification.title}
          message={notification.message}
          onClose={() => setNotification(null)}
        />
      )}

      <div className="max-w-4xl mx-auto relative z-10">
        {/* Header */}
        <div className="flex items-center mb-8">
          <button
            onClick={onBack}
            className="flex items-center text-white hover:text-blue-300 transition-all duration-300 mr-6 group hover:scale-105"
          >
            <ArrowLeft className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
            <span className="font-medium">返回</span>
          </button>
          <div className="flex items-center">
            <span className="text-3xl mr-3">{getFileTypeIcon()}</span>
            <div>
              <h1 className="text-2xl font-bold text-white bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                {fromFormat.toUpperCase()} 转 {outputFormat.toUpperCase()}
              </h1>
              <p className="text-gray-300">
                {fileType === 'VIDEO' && '视频格式转换'}
                {fileType === 'AUDIO' && '音频格式转换'}
                {fileType === 'IMAGE' && '图片格式转换'}
              </p>
            </div>
          </div>
        </div>

        {/* File Upload Area */}
        <div className="mb-8">
          <div
            onClick={handleClickSelect}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            className={`
              border-2 border-dashed rounded-xl p-12 text-center cursor-pointer
              transition-all duration-300 relative overflow-hidden
              ${isDragActiveLocal
                ? 'border-blue-400 bg-blue-400 bg-opacity-20 scale-102 shadow-glow'
                : 'border-gray-600 hover:border-gray-500 hover:bg-white hover:bg-opacity-5 hover:scale-101'
              }
            `}
          >
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept={getAcceptedFileTypes()['*/*']?.join(',')}
              className="hidden"
              onChange={onFileInputChange}
            />

            {/* Background animation */}
            <div className={`absolute inset-0 transition-opacity duration-400 ${isDragActiveLocal ? 'opacity-100' : 'opacity-0'}`}>
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 opacity-10 animate-shimmer"></div>
            </div>

            <div className="relative z-10">
              <Upload className={`w-16 h-16 text-gray-400 mx-auto mb-4 transition-all duration-300 hover:text-gray-300`} />
              <h3 className="text-xl font-semibold text-white mb-2 transition-colors duration-300">
                拖拽文件到此处，或点击选择
              </h3>

              <p className="text-gray-400 transition-colors duration-300">
                支持 {getSupportedInputFormats().join(', ')} 格式，最大 500MB
              </p>

              {/* Format indicators */}
              <div className="flex justify-center space-x-2 mt-4">
                {getSupportedInputFormats().slice(0, 4).map((format) => (
                  <span
                    key={format}
                    className="px-2 py-1 text-xs bg-gray-700 bg-opacity-50 rounded text-gray-300"
                  >
                    {format}
                  </span>
                ))}
                {getSupportedInputFormats().length > 4 && (
                  <span className="px-2 py-1 text-xs bg-gray-700 bg-opacity-50 rounded text-gray-300">
                    +{getSupportedInputFormats().length - 4}
                  </span>
                )}
              </div>
            </div>

            {errorMessage && (
              <div className="mt-4 p-3 bg-red-500 bg-opacity-20 border border-red-500 rounded-lg relative z-10">
                <p className="text-red-300 text-sm">{errorMessage}</p>
              </div>
            )}
          </div>
        </div>

        {/* Selected Files */}
        {selectedFiles.length > 0 && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">
                已选择文件 ({selectedFiles.length})
              </h3>
              <button
                onClick={clearAllFiles}
                className="text-red-400 hover:text-red-300 transition-all duration-300 text-sm hover:scale-105"
              >
                清除所有
              </button>
            </div>
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {selectedFiles.map((file, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between bg-gray-800 bg-opacity-50 rounded-lg p-4 border border-gray-700 hover:border-gray-600 transition-all duration-300 hover:scale-101 hover:shadow-glow-sm"
                >
                  <div className="flex items-center flex-1 min-w-0">
                    <span className="text-2xl mr-3 flex-shrink-0">{getFileTypeIcon()}</span>
                    <div className="flex-1 min-w-0">
                      <p className="text-white font-medium truncate hover:text-blue-200 transition-colors duration-300" title={file.name}>
                        {file.name}
                      </p>
                      <div className="flex items-center space-x-4 text-gray-400 text-sm">
                        <span className="flex items-center">
                          <div className="w-2 h-2 bg-blue-400 rounded-full mr-1"></div>
                          {(file.size / 1024 / 1024).toFixed(2)} MB
                        </span>
                        <span className="px-2 py-1 bg-gray-700 bg-opacity-50 rounded text-xs">
                          {getFileExtension(file.name).toUpperCase()}
                        </span>
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={() => removeFile(index)}
                    className="text-red-400 hover:text-red-300 transition-all duration-300 ml-4 flex-shrink-0 hover:scale-110 hover:rotate-90"
                    title="移除文件"
                  >
                    ✕
                  </button>
                </div>
              ))}
            </div>

            {/* File Statistics */}
            <div className="mt-4 p-3 bg-gray-800 bg-opacity-30 rounded-lg border border-gray-700 hover:border-gray-600 transition-all duration-300">
              <div className="flex justify-between text-sm text-gray-400">
                <span className="flex items-center">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                  总文件数: {selectedFiles.length}
                </span>
                <span className="flex items-center">
                  <div className="w-2 h-2 bg-purple-400 rounded-full mr-2"></div>
                  总大小: {(selectedFiles.reduce((total, file) => total + file.size, 0) / 1024 / 1024).toFixed(2)} MB
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Conversion Options */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">转换设置</h3>
            <button
              onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
              className="flex items-center text-blue-400 hover:text-blue-300 transition-all duration-300 hover:scale-105 group"
            >
              <Settings className={`w-4 h-4 mr-2 transition-transform duration-300 ${showAdvancedOptions ? 'rotate-180' : 'group-hover:rotate-90'}`} />
              {showAdvancedOptions ? '隐藏高级选项' : '显示高级选项'}
            </button>
          </div>

          <div className="bg-gray-800 bg-opacity-50 rounded-lg p-6 border border-gray-700 hover:border-gray-600 transition-all duration-300 backdrop-blur-sm">
            {/* Output Format Selection */}
            <div className="mb-4">
              <label className="block text-white font-medium mb-2">输出格式</label>
              <select
                value={outputFormat}
                onChange={(e) => setOutputFormat(e.target.value)}
                className="w-full bg-gray-700 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {getSupportedOutputFormats(fileType).map(format => (
                  <option key={format} value={format}>
                    {format.toUpperCase()}
                  </option>
                ))}
              </select>
            </div>

            {/* Advanced Options */}
            {showAdvancedOptions && (
              <div className="space-y-4 pt-4 border-t border-gray-700">
                {fileType === 'VIDEO' && (
                  <>
                    <div>
                      <label className="block text-white font-medium mb-2">分辨率</label>
                      <select
                        value={conversionOptions.resolution || ''}
                        onChange={(e) => setConversionOptions(prev => ({ ...prev, resolution: e.target.value }))}
                        className="w-full bg-gray-700 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">保持原始</option>
                        <option value="1920x1080">1920x1080 (1080p)</option>
                        <option value="1280x720">1280x720 (720p)</option>
                        <option value="854x480">854x480 (480p)</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-white font-medium mb-2">码率</label>
                      <input
                        type="text"
                        placeholder="例如: 2000k"
                        value={conversionOptions.bitrate || ''}
                        onChange={(e) => setConversionOptions(prev => ({ ...prev, bitrate: e.target.value }))}
                        className="w-full bg-gray-700 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </>
                )}

                {fileType === 'AUDIO' && (
                  <>
                    <div>
                      <label className="block text-white font-medium mb-2">音频码率</label>
                      <select
                        value={conversionOptions.audioBitrate || ''}
                        onChange={(e) => setConversionOptions(prev => ({ ...prev, audioBitrate: e.target.value }))}
                        className="w-full bg-gray-700 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">保持原始</option>
                        <option value="320k">320 kbps</option>
                        <option value="256k">256 kbps</option>
                        <option value="192k">192 kbps</option>
                        <option value="128k">128 kbps</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-white font-medium mb-2">采样率</label>
                      <select
                        value={conversionOptions.sampleRate || ''}
                        onChange={(e) => setConversionOptions(prev => ({ ...prev, sampleRate: parseInt(e.target.value) }))}
                        className="w-full bg-gray-700 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">保持原始</option>
                        <option value="48000">48000 Hz</option>
                        <option value="44100">44100 Hz</option>
                        <option value="22050">22050 Hz</option>
                      </select>
                    </div>
                  </>
                )}

                {fileType === 'IMAGE' && (
                  <>
                    {/* Resolution Presets */}
                    <div>
                      <label className="block text-white font-medium mb-2">预设分辨率</label>
                      <select
                        value=""
                        onChange={(e) => {
                          if (e.target.value) {
                            const [width, height] = e.target.value.split('x').map(Number)
                            setConversionOptions(prev => ({ ...prev, width, height }))
                          }
                        }}
                        className="w-full bg-gray-700 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">选择预设分辨率</option>
                        <option value="3840x2160">4K Ultra HD (3840x2160)</option>
                        <option value="2560x1440">2K QHD (2560x1440)</option>
                        <option value="1920x1080">Full HD (1920x1080)</option>
                        <option value="1280x720">HD (1280x720)</option>
                        <option value="854x480">SD (854x480)</option>
                        <option value="640x360">Mobile (640x360)</option>
                      </select>
                    </div>

                    {/* Custom Dimensions */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-white font-medium mb-2">自定义宽度</label>
                        <input
                          type="number"
                          placeholder="像素"
                          value={conversionOptions.width || ''}
                          onChange={(e) => setConversionOptions(prev => ({ ...prev, width: parseInt(e.target.value) || undefined }))}
                          className="w-full bg-gray-700 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-white font-medium mb-2">自定义高度</label>
                        <input
                          type="number"
                          placeholder="像素"
                          value={conversionOptions.height || ''}
                          onChange={(e) => setConversionOptions(prev => ({ ...prev, height: parseInt(e.target.value) || undefined }))}
                          className="w-full bg-gray-700 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    </div>

                    {/* Quality Settings */}
                    <div>
                      <label className="block text-white font-medium mb-2">图片质量</label>
                      <div className="mb-2">
                        <select
                          value=""
                          onChange={(e) => {
                            if (e.target.value) {
                              setConversionOptions(prev => ({ ...prev, quality: parseInt(e.target.value) }))
                            }
                          }}
                          className="w-full bg-gray-700 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="">选择质量预设</option>
                          <option value="95">最高质量 (95%)</option>
                          <option value="85">高质量 (85%)</option>
                          <option value="75">中等质量 (75%)</option>
                          <option value="60">低质量 (60%)</option>
                          <option value="40">最低质量 (40%)</option>
                        </select>
                      </div>
                      <input
                        type="range"
                        min="1"
                        max="100"
                        value={conversionOptions.quality || 80}
                        onChange={(e) => setConversionOptions(prev => ({ ...prev, quality: parseInt(e.target.value) }))}
                        className="w-full"
                      />
                      <div className="flex justify-between text-gray-400 text-sm mt-1">
                        <span>低质量</span>
                        <span className="font-medium">{conversionOptions.quality || 80}%</span>
                        <span>高质量</span>
                      </div>
                    </div>

                    {/* Compression Level for PNG */}
                    {outputFormat.toLowerCase() === 'png' && (
                      <div>
                        <label className="block text-white font-medium mb-2">压缩级别 (PNG)</label>
                        <input
                          type="range"
                          min="0"
                          max="9"
                          value={conversionOptions.compressionLevel || 6}
                          onChange={(e) => setConversionOptions(prev => ({ ...prev, compressionLevel: parseInt(e.target.value) }))}
                          className="w-full"
                        />
                        <div className="flex justify-between text-gray-400 text-sm mt-1">
                          <span>快速</span>
                          <span className="font-medium">{conversionOptions.compressionLevel || 6}</span>
                          <span>最佳压缩</span>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            )}
          </div>
        </div>



        {/* Action Buttons */}
        <div className="flex justify-center space-x-4">
          <button
            onClick={handleConvert}
            disabled={selectedFiles.length === 0 || isConverting}
            className={`
              flex items-center px-8 py-3 rounded-lg font-semibold transition-all duration-300 relative overflow-hidden group
              ${selectedFiles.length === 0 || isConverting
                ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                : `bg-gradient-to-r ${getFileTypeColor()} text-white hover:shadow-glow transform hover:scale-105 hover:-translate-y-1`
              }
            `}
          >
            {/* Button shimmer effect */}
            {!(selectedFiles.length === 0 || isConverting) && (
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white via-opacity-10 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer transition-opacity duration-300"></div>
            )}

            <Play className={`w-5 h-5 mr-2 relative z-10 transition-transform duration-300 ${isConverting ? '' : 'group-hover:scale-110'}`} />
            <span className="relative z-10">
              {isConverting ? '转换中...' : '开始转换'}
            </span>
          </button>

          <button
            onClick={() => ConversionService.openOutputFolder()}
            className="flex items-center px-8 py-3 rounded-lg font-semibold bg-gray-700 text-white hover:bg-gray-600 transition-all duration-300 hover:scale-105 hover:-translate-y-1 hover:shadow-glow-sm group relative overflow-hidden"
          >
            {/* Button shimmer effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white via-opacity-5 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer transition-opacity duration-300"></div>

            <FolderOpen className="w-5 h-5 mr-2 relative z-10 group-hover:scale-110 transition-transform duration-300" />
            <span className="relative z-10">打开输出目录</span>
          </button>
        </div>
      </div>

      {/* Progress Notifications */}
      <ProgressNotification onShowDetails={() => setShowProgressDetails(true)} />

      {/* Detailed Progress Modal */}
      <ConversionProgress
        isVisible={showProgressDetails}
        onClose={() => setShowProgressDetails(false)}
      />
      {/* Trial Limitation Modal */}
      <TrialLimitModal
        isVisible={showTrialModal}
        categoryName={trialService.getCategoryDisplayName(fileType as TrialCategory)}
        onClose={() => setShowTrialModal(false)}
      />
    </div>
  )
}

export default ConversionPage