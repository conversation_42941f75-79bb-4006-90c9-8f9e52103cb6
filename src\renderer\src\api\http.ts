import { post } from './base'

// 通用返回结构
export interface ApiResp<T = any> {
  code: number
  message: string
  time?: number
  data: T
}

export interface WxAuthInitResp {
  url: string
  token: string
}

// 获取微信登录的二维码链接与会话 token
export const getWxAuthUrl = async () => {
  return await post<ApiResp<WxAuthInitResp>>('/getWxAuthUrl')
}

// 轮询检查扫码状态（接口路径按需调整）
export interface WxAuthStatusResp {
  flag: boolean
  userinfo?: {
    token: string
    nickname: string
    avatar?: string
  }
}

export const checkWxAuthStatus = async (token: string) => {
  return await post<ApiResp<WxAuthStatusResp>>('/getWxAuthResult', { token })
}

// 个人用户信息
export interface UserInfo {
  user: {
    nickname: string
    avatar: string
    is_vip: number
    vip_status: number
  },
  vip: {
    over_time: number
    over_time_date: string
    over_day: number
  } | null
}

export const getUserInfo = async () => {
  return await post<ApiResp<UserInfo>>('/get_user_info')
}

// VIP 套餐信息
export interface VipPackage {
  id: number
  title: string
  amount: string
  amount_original: string
  type: number
}

export interface VipPackageList {
  list: VipPackage[]
}

// 获取VIP套餐信息
export const getBuyVipInfo = async () => {
  return await post<ApiResp<VipPackageList>>('/vip_config')
}

// 会员购买
type OrderType = {
  order_id: number
  pay_sn: string
  amount: string
}

export interface VipOrder {
  order: OrderType
  payment: string
  paydata: string
}

export const getVipOrder = async (id: number, payment: string) => {
  return await post<ApiResp<VipOrder>>('/vip_order', { id, payment })
}

// 查询订单
type OrderDetailType = {
  order_id: number
  pay_sn: string
  trade_no: null
  amount: string
  day: number
}

type OrderResultType = {
  order: OrderDetailType
  pay_status: string
}
export const getVipOrderInfo = async (order_id: number) => {
  return await post<ApiResp<OrderResultType>>('/vip_order_detail', { order_id })
}