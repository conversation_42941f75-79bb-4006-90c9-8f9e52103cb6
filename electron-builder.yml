appId: com.konvertor.格式转换大师
productName: 格式转换大师
directories:
  buildResources: build
# 压缩优化
compression: maximum
# 确保包含必要的依赖
includeSubNodeModules: true
# 文件包含规则
files:
  - out/**
  - node_modules/**
  - package.json
  - '!**/.vscode/*'
  - '!src/*'
  - '!electron.vite.config.{js,ts,mjs,cjs}'
  - '!{.eslintcache,eslint.config.mjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
  - '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
  - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
asarUnpack:
  - resources/**
  # 解压必要的 native 模块和二进制文件
  - node_modules/sharp/**
  - node_modules/@ffmpeg-installer/**
  - node_modules/fluent-ffmpeg/**
win:
  executableName: 格式转换大师
  target:
    - target: nsis
      arch:
        - x64
nsis:
  artifactName: 格式转换大师-${version}-setup.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: com.maruitech.formatconversionmaster
  oneClick: false
  allowToChangeInstallationDirectory: true
  perMachine: false
  installerIcon: build/icon.ico
  uninstallerIcon: build/icon.ico
  installerHeaderIcon: build/icon.ico
  # 允许用户选择是否创建快捷方式
  createDesktopShortcut: 'always'
  createStartMenuShortcut: true
  # 支持中文路径和Unicode
  unicode: true
  # 设置默认安装目录为中文路径
  installerLanguages:
    - zh_CN
mac:
  entitlementsInherit: build/entitlements.mac.plist
  extendInfo:
    - NSCameraUsageDescription: Application requests access to the device's camera.
    - NSMicrophoneUsageDescription: Application requests access to the device's microphone.
    - NSDocumentsFolderUsageDescription: Application requests access to the user's Documents folder.
    - NSDownloadsFolderUsageDescription: Application requests access to the user's Downloads folder.
  notarize: false
dmg:
  artifactName: ${name}-${version}.${ext}
linux:
  target:
    - AppImage
    - snap
    - deb
  maintainer: electronjs.org
  category: Utility
appImage:
  artifactName: ${name}-${version}.${ext}
npmRebuild: false
publish:
  provider: generic
  url: https://example.com/auto-updates
electronDownload:
  mirror: https://npmmirror.com/mirrors/electron/
