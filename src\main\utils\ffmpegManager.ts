import * as path from 'path'
import * as fs from 'fs'
import * as os from 'os'
import { app } from 'electron'

export class FFmpegManager {
  private static ffmpegPath: string | null = null
  private static ffprobePath: string | null = null

  static async initialize(): Promise<{ ffmpegPath: string | null, ffprobePath: string | null }> {
    try {
      // Try to find system FFmpeg first
      const systemPaths = await this.findSystemFFmpeg()
      if (systemPaths.ffmpegPath && systemPaths.ffprobePath) {
        this.ffmpegPath = systemPaths.ffmpegPath
        this.ffprobePath = systemPaths.ffprobePath
        console.log('Using system FFmpeg:', systemPaths)
        return systemPaths
      }

      // Try to find bundled FFmpeg
      const bundledPaths = await this.findBundledFFmpeg()
      if (bundledPaths.ffmpegPath && bundledPaths.ffprobePath) {
        this.ffmpegPath = bundledPaths.ffmpegPath
        this.ffprobePath = bundledPaths.ffprobePath
        console.log('Using bundled FFmpeg:', bundledPaths)
        return bundledPaths
      }

      console.warn('FFmpeg not found. Video/audio conversion will not be available.')
      return { ffmpegPath: null, ffprobePath: null }
    } catch (error) {
      console.error('Failed to initialize FFmpeg:', error)
      return { ffmpegPath: null, ffprobePath: null }
    }
  }

  private static async findSystemFFmpeg(): Promise<{ ffmpegPath: string | null, ffprobePath: string | null }> {
    const platform = os.platform()
    let possiblePaths: string[] = []

    switch (platform) {
      case 'win32':
        possiblePaths = [
          'ffmpeg.exe',
          'C:\\ffmpeg\\bin\\ffmpeg.exe',
          'C:\\Program Files\\ffmpeg\\bin\\ffmpeg.exe',
          path.join(os.homedir(), 'ffmpeg', 'bin', 'ffmpeg.exe')
        ]
        break
      case 'darwin':
        possiblePaths = [
          'ffmpeg',
          '/usr/local/bin/ffmpeg',
          '/opt/homebrew/bin/ffmpeg',
          '/usr/bin/ffmpeg'
        ]
        break
      case 'linux':
        possiblePaths = [
          'ffmpeg',
          '/usr/bin/ffmpeg',
          '/usr/local/bin/ffmpeg',
          '/snap/bin/ffmpeg'
        ]
        break
    }

    for (const ffmpegPath of possiblePaths) {
      try {
        if (await this.testFFmpegPath(ffmpegPath)) {
          const ffprobePath = this.getFFprobePathFromFFmpeg(ffmpegPath)
          if (await this.testFFprobePath(ffprobePath)) {
            return { ffmpegPath, ffprobePath }
          }
        }
      } catch (error) {
        // Continue to next path
      }
    }

    return { ffmpegPath: null, ffprobePath: null }
  }

  private static async findBundledFFmpeg(): Promise<{ ffmpegPath: string | null, ffprobePath: string | null }> {
    const platform = os.platform()
    const appPath = app.getAppPath()
    const resourcesPath = process.resourcesPath || appPath
    
    let ffmpegName = 'ffmpeg'
    let ffprobeName = 'ffprobe'
    let platformDir = ''
    
    // 根据平台设置可执行文件名和平台目录
    switch (platform) {
      case 'win32':
        ffmpegName += '.exe'
        ffprobeName += '.exe'
        platformDir = 'win32-x64'
        break
      case 'darwin':
        platformDir = 'darwin-x64'
        break
      case 'linux':
        platformDir = 'linux-x64'
        break
    }

    console.log('=== 查找打包的 FFmpeg ===')
    console.log('平台:', platform)
    console.log('应用路径:', appPath)
    console.log('资源路径:', resourcesPath)
    console.log('是否打包:', app.isPackaged)

    const possiblePaths = [
      // @ffmpeg-installer 包的路径 (打包后) - 优先级最高
      path.join(resourcesPath, 'app.asar.unpacked', 'node_modules', '@ffmpeg-installer', platformDir, ffmpegName),
      
      // 开发环境的 @ffmpeg-installer 路径
      path.join(appPath, 'node_modules', '@ffmpeg-installer', platformDir, ffmpegName),
      path.join(process.cwd(), 'node_modules', '@ffmpeg-installer', platformDir, ffmpegName),
      
      // 备选：@ffmpeg-installer 的 ffmpeg 子目录
      path.join(resourcesPath, 'app.asar.unpacked', 'node_modules', '@ffmpeg-installer', 'ffmpeg', ffmpegName),
      path.join(appPath, 'node_modules', '@ffmpeg-installer', 'ffmpeg', ffmpegName),
      
      // 自定义 extraResources 路径
      path.join(resourcesPath, 'ffmpeg', platformDir, ffmpegName),
      path.join(resourcesPath, 'ffmpeg', ffmpegName),
      
      // 传统路径
      path.join(appPath, 'resources', 'ffmpeg', platform, ffmpegName),
      path.join(appPath, 'ffmpeg', platform, ffmpegName),
      path.join(resourcesPath, 'app.asar.unpacked', 'resources', 'ffmpeg', platform, ffmpegName),
    ]

    console.log('尝试的路径:')
    for (const ffmpegPath of possiblePaths) {
      console.log(`  检查: ${ffmpegPath}`)
      try {
        if (fs.existsSync(ffmpegPath)) {
          console.log(`  ✅ FFmpeg 找到: ${ffmpegPath}`)
          
          // 查找对应的 ffprobe
          const ffprobeDir = path.dirname(ffmpegPath)
          let ffprobePath = path.join(ffprobeDir, ffprobeName)
          
          console.log(`  检查 FFprobe: ${ffprobePath}`)
          
          // 对于 @ffmpeg-installer，ffprobe 可能不存在，我们可以只使用 ffmpeg
          let ffprobeWorks = false
          if (fs.existsSync(ffprobePath)) {
            console.log(`  ✅ FFprobe 找到: ${ffprobePath}`)
            
            // 在 Unix 系统上设置可执行权限
            if (platform !== 'win32') {
              try {
                fs.chmodSync(ffmpegPath, '755')
                fs.chmodSync(ffprobePath, '755')
                console.log('  ✅ 设置可执行权限成功')
              } catch (error) {
                console.warn('  ⚠️ 设置可执行权限失败:', error)
              }
            }
            
            // 测试 FFprobe 是否可用
            console.log('  🧪 测试 FFprobe 可用性...')
            ffprobeWorks = await this.testFFprobePath(ffprobePath)
            console.log(`  FFprobe 测试结果: ${ffprobeWorks ? '✅' : '❌'}`)
          } else {
            console.log(`  ⚠️ FFprobe 未找到，将使用 FFmpeg 内置的 probe 功能`)
            // 对于某些 @ffmpeg-installer 版本，可能没有单独的 ffprobe
            ffprobePath = ffmpegPath // 使用 ffmpeg 本身
            ffprobeWorks = true // 假设 ffmpeg 可以处理 probe 功能
          }
          
          // 测试 FFmpeg 是否可用
          console.log('  🧪 测试 FFmpeg 可用性...')
          const ffmpegWorks = await this.testFFmpegPath(ffmpegPath)
          console.log(`  FFmpeg 测试结果: ${ffmpegWorks ? '✅' : '❌'}`)
          
          if (ffmpegWorks && ffprobeWorks) {
            console.log(`  🎯 使用 FFmpeg: ${ffmpegPath}`)
            console.log(`  🎯 使用 FFprobe: ${ffprobePath}`)
            return { ffmpegPath, ffprobePath }
          }
        } else {
          console.log(`  ❌ FFmpeg 未找到`)
        }
      } catch (error) {
        console.log(`  ❌ 检查路径时出错: ${error}`)
      }
    }

    // 如果没有找到，尝试诊断目录结构
    console.log('\n=== 目录结构诊断 ===')
    try {
      if (fs.existsSync(resourcesPath)) {
        console.log(`资源目录内容 (${resourcesPath}):`)
        const resourcesContents = fs.readdirSync(resourcesPath)
        resourcesContents.forEach(item => console.log(`  - ${item}`))
        
        const asarUnpackedPath = path.join(resourcesPath, 'app.asar.unpacked')
        if (fs.existsSync(asarUnpackedPath)) {
          console.log(`\napp.asar.unpacked 内容:`)
          const unpackedContents = fs.readdirSync(asarUnpackedPath)
          unpackedContents.forEach(item => console.log(`  - ${item}`))
          
          const nodeModulesPath = path.join(asarUnpackedPath, 'node_modules')
          if (fs.existsSync(nodeModulesPath)) {
            console.log(`\nnode_modules 内容:`)
            const nodeModulesContents = fs.readdirSync(nodeModulesPath)
            nodeModulesContents.forEach(item => console.log(`  - ${item}`))
            
            const ffmpegInstallerPath = path.join(nodeModulesPath, '@ffmpeg-installer')
            if (fs.existsSync(ffmpegInstallerPath)) {
              console.log(`\n@ffmpeg-installer 内容:`)
              const installerContents = fs.readdirSync(ffmpegInstallerPath)
              installerContents.forEach(item => console.log(`  - ${item}`))
              
              const platformPath = path.join(ffmpegInstallerPath, platformDir)
              if (fs.existsSync(platformPath)) {
                console.log(`\n${platformDir} 内容:`)
                const platformContents = fs.readdirSync(platformPath)
                platformContents.forEach(item => console.log(`  - ${item}`))
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('目录结构诊断失败:', error)
    }

    console.log('❌ 未找到可用的 FFmpeg')
    return { ffmpegPath: null, ffprobePath: null }
  }

  private static getFFprobePathFromFFmpeg(ffmpegPath: string): string {
    const dir = path.dirname(ffmpegPath)
    const platform = os.platform()
    const ffprobeName = platform === 'win32' ? 'ffprobe.exe' : 'ffprobe'
    
    // If ffmpeg is just a command name, assume ffprobe is also in PATH
    if (ffmpegPath === 'ffmpeg' || ffmpegPath === 'ffmpeg.exe') {
      return ffprobeName
    }
    
    return path.join(dir, ffprobeName)
  }

  private static async testFFmpegPath(ffmpegPath: string): Promise<boolean> {
    return new Promise((resolve) => {
      const { spawn } = require('child_process')
      const child = spawn(ffmpegPath, ['-version'], { 
        stdio: 'pipe',
        timeout: 5000 
      })
      
      child.on('error', () => resolve(false))
      child.on('exit', (code) => resolve(code === 0))
      
      // Timeout fallback
      setTimeout(() => {
        child.kill()
        resolve(false)
      }, 5000)
    })
  }

  private static async testFFprobePath(ffprobePath: string): Promise<boolean> {
    return new Promise((resolve) => {
      const { spawn } = require('child_process')
      const child = spawn(ffprobePath, ['-version'], { 
        stdio: 'pipe',
        timeout: 5000 
      })
      
      child.on('error', () => resolve(false))
      child.on('exit', (code) => resolve(code === 0))
      
      // Timeout fallback
      setTimeout(() => {
        child.kill()
        resolve(false)
      }, 5000)
    })
  }

  static getFFmpegPath(): string | null {
    return this.ffmpegPath
  }

  static getFFprobePath(): string | null {
    return this.ffprobePath
  }

  static isAvailable(): boolean {
    return !!(this.ffmpegPath && this.ffprobePath)
  }

  static getInstallationInstructions(): string {
    const platform = os.platform()
    
    switch (platform) {
      case 'win32':
        return `请安装 FFmpeg：
1. 访问 https://ffmpeg.org/download.html
2. 下载 Windows 版本
3. 解压到 C:\\ffmpeg\\
4. 将 C:\\ffmpeg\\bin 添加到系统 PATH
5. 重启应用程序`

      case 'darwin':
        return `请安装 FFmpeg：
1. 使用 Homebrew: brew install ffmpeg
2. 或访问 https://ffmpeg.org/download.html 下载
3. 重启应用程序`

      case 'linux':
        return `请安装 FFmpeg：
1. Ubuntu/Debian: sudo apt install ffmpeg
2. CentOS/RHEL: sudo yum install ffmpeg
3. 或访问 https://ffmpeg.org/download.html
4. 重启应用程序`

      default:
        return '请访问 https://ffmpeg.org/download.html 安装 FFmpeg'
    }
  }
}