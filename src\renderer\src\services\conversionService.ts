import { IPC_CHANNELS } from '../../../shared/constants'
import { IPCService } from './ipcService'
import type { ConversionTask, ConversionOptions, SupportedFormat, FileType } from '../../../shared/types'

export class ConversionService {
  static async convertFile(
    inputPath: string,
    outputPath: string,
    inputFormat: SupportedFormat,
    outputFormat: SupportedFormat,
    fileType: FileType,
    options?: ConversionOptions
  ): Promise<ConversionTask> {
    return window.electron.ipcRenderer.invoke(
      IPC_CHANNELS.CONVERT_FILE,
      inputPath,
      outputPath,
      inputFormat,
      outputFormat,
      fileType,
      options
    )
  }

  static async getConversionProgress(taskId: string): Promise<number> {
    return window.electron.ipcRenderer.invoke(
      IPC_CHANNELS.GET_CONVERSION_PROGRESS,
      taskId
    )
  }

  static async cancelConversion(taskId: string): Promise<void> {
    return window.electron.ipcRenderer.invoke(
      IPC_CHANNELS.CANCEL_CONVERSION,
      taskId
    )
  }

  static async getSupportedFormats(): Promise<typeof import('../../../shared/constants').FILE_TYPES> {
    return IPCService.getSupportedFormats()
  }

  static async openOutputFolder(path?: string): Promise<void> {
    return IPCService.openOutputFolder(path)
  }

  static getPathForFile(file: File): string {
    return IPCService.getPathForFile(file)
  }
}