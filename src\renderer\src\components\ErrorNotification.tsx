import React, { useState, useEffect } from 'react'
import { X, AlertTriangle, XCircle, AlertCircle, Info } from 'lucide-react'
import { ErrorNotification as ErrorNotificationType } from '../utils/errorManager'

interface ErrorNotificationProps {
  notification: ErrorNotificationType
  onClose: (id: string) => void
}

const ErrorNotification: React.FC<ErrorNotificationProps> = ({
  notification,
  onClose
}) => {
  const [isVisible, setIsVisible] = useState(true)
  const [timeLeft, setTimeLeft] = useState<number | null>(null)

  useEffect(() => {
    if (notification.duration) {
      setTimeLeft(notification.duration / 1000)
      
      const timer = setTimeout(() => {
        handleClose()
      }, notification.duration)

      // 倒计时更新
      const countdownTimer = setInterval(() => {
        setTimeLeft(prev => {
          if (prev === null || prev <= 1) {
            clearInterval(countdownTimer)
            return null
          }
          return prev - 1
        })
      }, 1000)

      return () => {
        clearTimeout(timer)
        clearInterval(countdownTimer)
      }
    }
    return undefined
  }, [notification.duration])

  const handleClose = () => {
    setIsVisible(false)
    setTimeout(() => {
      onClose(notification.id)
    }, 300) // 等待动画完成
  }

  const getIcon = () => {
    switch (notification.type) {
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />
      case 'info':
        return <Info className="w-5 h-5 text-blue-500" />
      default:
        return <AlertCircle className="w-5 h-5 text-gray-500" />
    }
  }

  const getBackgroundColor = () => {
    switch (notification.type) {
      case 'error':
        return 'bg-red-50 border-red-200'
      case 'warning':
        return 'bg-yellow-50 border-yellow-200'
      case 'info':
        return 'bg-blue-50 border-blue-200'
      default:
        return 'bg-gray-50 border-gray-200'
    }
  }

  const getProgressColor = () => {
    switch (notification.type) {
      case 'error':
        return 'bg-red-500'
      case 'warning':
        return 'bg-yellow-500'
      case 'info':
        return 'bg-blue-500'
      default:
        return 'bg-gray-500'
    }
  }

  return (
    <div
      className={`
        transform transition-all duration-300 ease-in-out
        ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
        bg-white border rounded-lg shadow-lg p-4 mb-3 min-w-80 max-w-md
        ${getBackgroundColor()}
      `}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center space-x-3">
          {getIcon()}
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-medium text-gray-900 truncate">
              {notification.title}
            </h4>
            {timeLeft !== null && (
              <p className="text-xs text-gray-500">
                {timeLeft}秒后自动关闭
              </p>
            )}
          </div>
        </div>
        <button
          onClick={handleClose}
          className="p-1 hover:bg-gray-100 rounded transition-colors flex-shrink-0"
        >
          <X className="w-4 h-4 text-gray-400" />
        </button>
      </div>

      {/* Message */}
      <div className="mb-3">
        <p className="text-sm text-gray-700 leading-relaxed">
          {notification.message}
        </p>
      </div>

      {/* Actions */}
      {notification.actions && notification.actions.length > 0 && (
        <div className="flex items-center space-x-2">
          {notification.actions.map((action, index) => (
            <button
              key={index}
              onClick={() => {
                action.action()
                handleClose()
              }}
              className="px-3 py-1 text-xs font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-100 rounded transition-colors"
            >
              {action.label}
            </button>
          ))}
        </div>
      )}

      {/* Progress bar for timed notifications */}
      {notification.duration && timeLeft !== null && (
        <div className="mt-3">
          <div className="w-full bg-gray-200 rounded-full h-1">
            <div
              className={`h-1 rounded-full transition-all duration-1000 ease-linear ${getProgressColor()}`}
              style={{
                width: `${((notification.duration / 1000 - timeLeft) / (notification.duration / 1000)) * 100}%`
              }}
            />
          </div>
        </div>
      )}
    </div>
  )
}

export default ErrorNotification