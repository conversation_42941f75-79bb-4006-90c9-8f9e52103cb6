# 格式转换桌面应用程序规格说明

## 项目概述

开发一个现代化的桌面格式转换工具，支持视频、音频和图片格式之间的转换。应用程序基于 Electron 构建，提供直观的用户界面和高效的文件处理能力。

## 功能需求

### 核心功能

1. **视频格式转换**
   - 支持常见视频格式：MP4, AVI, MOV, MKV, WMV, FLV
   - 转换选项：分辨率调整、码率设置、帧率调整

2. **音频格式转换**
   - 支持常见音频格式：MP3, WAV, FLAC, AAC, OGG, M4A
   - 转换选项：比特率设置、采样率调整、声道设置

3. **图片格式转换**
   - 支持常见图片格式：JPG, PNG, GIF, BMP, WEBP, TIFF
   - 转换选项：尺寸调整、质量设置、压缩级别

### 用户界面设计

#### 首页布局

- **三大分类区域**：视频格式转换、音频格式转换、图片格式转换
- **卡片式设计**：每个转换类型作为独立卡片展示
  - 示例卡片：
    - 视频：MP4 → AVI, MOV → MP4, AVI → MKV
    - 音频：FLAC → MP3, WAV → AAC, MP3 → FLAC
    - 图片：PNG → JPG, JPG → WEBP, GIF → MP4

#### 设计风格

- **现代化界面**：简洁、直观的用户体验
- **酷炫元素**：渐变背景、动画效果、阴影和圆角
- **响应式设计**：适配不同窗口尺寸
- **深色/浅色主题**：支持主题切换

#### 自定义窗口设计

- **无边框窗口**：移除默认的系统窗口边框
- **自定义标题栏**：
  - 左侧：应用 Logo + 应用名称
  - 右侧：最小化、最大化、关闭按钮
  - 支持拖拽移动窗口
- **版本信息显示**：
  - 位置：右下角
  - 内容：显示当前应用版本（与 package.json 保持一致）
  - 样式：半透明、小字体

### 文件处理机制

#### Electron 文件处理流程

1. **文件上传**：
   - 渲染进程接收用户选择的文件
   - 使用 `webUtils.getPathForFile(file)` 获取真实文件路径
   - 将路径传递给主进程进行文件处理

2. **文件转换**：
   - 主进程负责调用转换库
   - 显示转换进度
   - 处理转换错误和异常

3. **文件存储**：
   - 默认输出路径：`用户桌面/格式转换/`
   - 支持自定义输出路径
   - 提供"打开下载目录"功能

### 技术栈

#### 前端技术

- **框架**：Electron + React/Vue (待确定)
- **样式**：Tailwind CSS (已配置)
- **UI组件**：现代化组件库
- **状态管理**：Context API 或 Zustand

#### 后端处理

- **视频转换**：FFmpeg (通过 fluent-ffmpeg)
- **音频转换**：FFmpeg 或 node-lame
- **图片转换**：Sharp 或 Jimp

#### 依赖包 (npm)

```json
{
  "dependencies": {
    "fluent-ffmpeg": "^2.1.2",
    "sharp": "^0.32.0",
    "electron-store": "^8.1.0",
    "electron-updater": "^6.1.0"
  }
}
```

## 用户故事

### 主要用户流程

1. **启动应用**：用户打开应用，看到三大分类的卡片界面
2. **选择转换类型**：点击具体的转换卡片（如 MP4 → AVI）
3. **上传文件**：拖拽或点击选择要转换的文件
4. **设置参数**：调整转换参数（可选）
5. **开始转换**：点击转换按钮，显示进度条
6. **完成转换**：转换完成后提示，可直接打开输出目录

### 界面交互

- **拖拽上传**：支持直接拖拽文件到应用窗口
- **批量处理**：支持同时转换多个文件
- **进度显示**：实时显示转换进度和剩余时间
- **错误处理**：友好的错误提示和解决建议

## 技术实现细节

### 常量管理

创建统一的常量文件来管理所有 IPC 通道名称和配置常量：

```typescript
// src/shared/constants.ts
export const IPC_CHANNELS = {
  CONVERT_FILE: 'convert-file',
  GET_FILE_PATH: 'get-file-path',
  OPEN_OUTPUT_FOLDER: 'open-output-folder',
  GET_CONVERSION_PROGRESS: 'get-conversion-progress',
  CANCEL_CONVERSION: 'cancel-conversion',
  GET_SUPPORTED_FORMATS: 'get-supported-formats',
  UPDATE_SETTINGS: 'update-settings',
  GET_SETTINGS: 'get-settings',
  GET_APP_VERSION: 'get-app-version',
  WINDOW_MINIMIZE: 'window-minimize',
  WINDOW_MAXIMIZE: 'window-maximize',
  WINDOW_CLOSE: 'window-close',
  WINDOW_UNMAXIMIZE: 'window-unmaximize'
} as const

export const FILE_TYPES = {
  VIDEO: {
    INPUT: ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm'],
    OUTPUT: ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm']
  },
  AUDIO: {
    INPUT: ['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a', 'wma'],
    OUTPUT: ['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a']
  },
  IMAGE: {
    INPUT: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff'],
    OUTPUT: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff']
  }
} as const

export const DEFAULT_PATHS = {
  OUTPUT_FOLDER: '格式转换',
  TEMP_FOLDER: '.format-converter-temp'
} as const

export const CONVERSION_STATUS = {
  IDLE: 'idle',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  ERROR: 'error',
  CANCELLED: 'cancelled'
} as const
```

### 文件路径处理

```typescript
// 渲染进程
import { IPC_CHANNELS } from '../shared/constants'

const getPathForFile = (file: File): string => {
  return webUtils.getPathForFile(file)
}

// 主进程接收文件路径进行处理
ipcMain.handle(IPC_CHANNELS.CONVERT_FILE, async (event, filePath, outputPath, options) => {
  // 文件转换逻辑
})

// 其他 IPC 处理
ipcMain.handle(IPC_CHANNELS.OPEN_OUTPUT_FOLDER, async () => {
  // 打开输出文件夹逻辑
})
```

### 版本信息获取

```typescript
// 主进程 - 获取应用版本
import { app } from 'electron'
import packageJson from '../../package.json'

ipcMain.handle(IPC_CHANNELS.GET_APP_VERSION, () => {
  // 确保版本与 package.json 保持一致
  return {
    version: app.getVersion(), // 或者 packageJson.version
    name: app.getName()
  }
})

// 渲染进程 - 显示版本信息
const [appInfo, setAppInfo] = useState({ version: '', name: '' })

useEffect(() => {
  ipcRenderer.invoke(IPC_CHANNELS.GET_APP_VERSION).then(setAppInfo)
}, [])
```

### 自定义窗口控制

```typescript
// 主进程 - 窗口创建配置
const createWindow = () => {
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    frame: false, // 移除默认边框
    titleBarStyle: 'hidden', // 隐藏标题栏
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  })
}

// 主进程 - 窗口控制 IPC 处理
ipcMain.handle(IPC_CHANNELS.WINDOW_MINIMIZE, (event) => {
  const window = BrowserWindow.fromWebContents(event.sender)
  window?.minimize()
})

ipcMain.handle(IPC_CHANNELS.WINDOW_MAXIMIZE, (event) => {
  const window = BrowserWindow.fromWebContents(event.sender)
  if (window?.isMaximized()) {
    window.unmaximize()
  } else {
    window?.maximize()
  }
})

ipcMain.handle(IPC_CHANNELS.WINDOW_CLOSE, (event) => {
  const window = BrowserWindow.fromWebContents(event.sender)
  window?.close()
})

// 渲染进程 - 自定义标题栏组件
const CustomTitleBar = () => {
  const handleMinimize = () => ipcRenderer.invoke(IPC_CHANNELS.WINDOW_MINIMIZE)
  const handleMaximize = () => ipcRenderer.invoke(IPC_CHANNELS.WINDOW_MAXIMIZE)
  const handleClose = () => ipcRenderer.invoke(IPC_CHANNELS.WINDOW_CLOSE)

  return (
    <div className="flex justify-between items-center h-8 bg-gray-900 drag-region">
      <div className="flex items-center px-4">
        <img src="./logo.png" alt="Logo" className="w-5 h-5 mr-2" />
        <span className="text-white text-sm">格式转换器</span>
      </div>
      <div className="flex">
        <button onClick={handleMinimize} className="window-control-btn">−</button>
        <button onClick={handleMaximize} className="window-control-btn">□</button>
        <button onClick={handleClose} className="window-control-btn hover:bg-red-600">×</button>
      </div>
    </div>
  )
}
```

### 输出目录管理

```typescript
const defaultOutputPath = path.join(os.homedir(), 'Desktop', '格式转换')
```

## 开发阶段

### 第一阶段：基础框架

- [x] 设置项目结构

- [x] 创建主界面布局

- [x] 实现文件选择功能

- [x] 配置 Electron IPC 通信

### 第二阶段：转换功能

- [x] 集成 FFmpeg 进行视频/音频转换

- [x] 集成 Sharp 进行图片转换

- [x] 实现转换进度显示 <!-- taskStatus: in_progress -->
- [x] 添加错误处理机制

### 第三阶段：用户体验

- [x] 优化界面设计和动画

- [x] 添加设置页面

- [x] 实现主题切换

- [ ] 添加快捷键支持

### 第四阶段：高级功能

- [ ] 批量转换功能
- [ ] 转换历史记录
- [ ] 自动更新机制
- [ ] 性能优化

## 验收标准

1. **功能完整性**：所有三大类格式转换功能正常工作
2. **用户体验**：界面美观、操作流畅、响应及时
3. **稳定性**：处理大文件时不崩溃，错误处理完善
4. **性能**：转换速度合理，内存使用控制在合理范围
5. **兼容性**：支持 Windows、macOS、Linux 三大平台

## 风险和挑战

1. **FFmpeg 集成**：需要正确配置 FFmpeg 二进制文件
2. **大文件处理**：需要优化内存使用和处理速度
3. **跨平台兼容**：确保在不同操作系统上正常运行
4. **用户权限**：处理文件读写权限问题

---

_此规格说明将作为开发指导文档，在开发过程中可能会根据实际情况进行调整和完善。_
