import { FILE_TYPES } from '../../../shared/constants'
import type { FileType, SupportedFormat } from '../../../shared/types'

export const getFileExtension = (filename: string): string => {
  return filename.split('.').pop()?.toLowerCase() || ''
}

export const getFileType = (extension: string): FileType | null => {
  const ext = extension.toLowerCase()
  
  if ((FILE_TYPES.VIDEO.INPUT as readonly string[]).includes(ext)) {
    return 'VIDEO'
  }
  if ((FILE_TYPES.AUDIO.INPUT as readonly string[]).includes(ext)) {
    return 'AUDIO'
  }
  if ((FILE_TYPES.IMAGE.INPUT as readonly string[]).includes(ext)) {
    return 'IMAGE'
  }
  
  return null
}

export const isValidFormat = (extension: string, fileType: FileType): boolean => {
  const ext = extension.toLowerCase()
  return (FILE_TYPES[fileType].INPUT as readonly string[]).includes(ext)
}

export const getSupportedOutputFormats = (fileType: FileType): readonly string[] => {
  return FILE_TYPES[fileType].OUTPUT
}

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export const generateOutputPath = (inputPath: string, outputFormat: string, outputDir: string): string => {
  const filename = inputPath.split(/[/\\]/).pop() || 'output'
  const nameWithoutExt = filename.split('.').slice(0, -1).join('.')
  return `${outputDir}/${nameWithoutExt}.${outputFormat}`
}