/**
 * TalkingData 事件追踪工具函数
 */

// 声明全局 TDAPP 对象
declare global {
  interface Window {
    TDAPP?: {
      onEvent: (eventId: string, label?: string, mapKv?: Record<string, any>) => void;
    };
  }
}

/**
 * 发送自定义事件到 TalkingData
 * @param eventId 事件ID，建议使用有意义的名称
 * @param label 事件标签，可选
 * @param mapKv 事件参数，可选
 */
export const trackEvent = (eventId: string, label?: string, mapKv?: Record<string, any>) => {
  try {
    if (window.TDAPP && typeof window.TDAPP.onEvent === 'function') {
      window.TDAPP.onEvent(eventId, label, mapKv);
    } else {
      console.warn('TalkingData SDK 未初始化或不可用');
    }
  } catch (error) {
    console.error('发送TalkingData事件失败:', error);
  }
};

/**
 * 追踪菜单点击事件
 * @param menuName 菜单名称
 * @param menuKey 菜单key
 * @param url 菜单对应的URL
 */
export const trackMenuClick = (menuName: string, menuKey: string, url: string) => {
  trackEvent(
    `${menuName}_page`,
    '',
    {
      menu_name: menuName,
      menu_key: menuKey,
      page_url: url,
      timestamp: Date.now()
    }
  );
};

/**
 * 追踪页面保存操作事件
 * @param pageName 页面名称
 * @param pageKey 页面key
 * @param actionType 操作类型，如 'save', 'export', 'download' 等
 * @param fileCount 文件数量，可选
 */
export const trackPageSaveAction = (
  pageName: string,
  pageKey: string,
  actionType: string,
  fileCount?: number
) => {
  trackEvent(
    `${pageName}_${actionType}_action`, // 事件名：page_save_图片转pdf_save
    ``,
    {
      page_name: pageName,
      page_key: pageKey,
      action_type: actionType,
      file_count: fileCount,
      timestamp: Date.now()
    }
  );
};

/**
 * 登录相关埋点
 * @param stage 阶段：open | success | failure | logout
 * @param method 方式：wechat | phone | other，可选
 * @param reason 失败原因或补充信息，可选
 */
export const trackLogin = (
  stage: 'open' | 'success' | 'failure' | 'logout',
  method?: 'wechat' | 'phone' | 'other' | string,
  reason?: string
) => {
  trackEvent(
    `login_${stage}`,
    '',
    {
      method,
      reason,
      timestamp: Date.now(),
    }
  );
};

/**
 * 开通 VIP 相关埋点
 * @param stage 阶段：open | order_created | success | cancel | close | fail
 * @param paymentMethod 支付方式：wechat | alipay，可选
 * @param orderId 订单ID，可选
 */
export const trackVipPurchase = (
  stage: 'open' | 'order_created' | 'success' | 'cancel' | 'close' | 'fail',
  paymentMethod?: 'wechat' | 'alipay' | string,
  orderId?: number | string
) => {
  trackEvent(
    `vip_purchase_${stage}`,
    '',
    {
      payment_method: paymentMethod,
      order_id: orderId,
      timestamp: Date.now(),
    }
  );
};