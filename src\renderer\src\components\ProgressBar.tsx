import React from 'react'
import { CheckCircle, XCircle, Clock, Pause } from 'lucide-react'

interface ProgressBarProps {
  progress: number
  status: 'idle' | 'processing' | 'completed' | 'error' | 'cancelled'
  fileName?: string
  currentTime?: string
  totalTime?: string
  speed?: string
  fps?: number
  bitrate?: string
  size?: string
  eta?: string
  error?: string
  showDetails?: boolean
  className?: string
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  status,
  fileName,
  currentTime,
  totalTime,
  speed,
  fps,
  bitrate,
  size,
  eta,
  error,
  showDetails = false,
  className = ''
}) => {
  const getStatusIcon = () => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-400" />
      case 'error':
        return <XCircle className="w-5 h-5 text-red-400" />
      case 'cancelled':
        return <Pause className="w-5 h-5 text-yellow-400" />
      case 'processing':
        return <Clock className="w-5 h-5 text-blue-400 animate-spin" />
      default:
        return <Clock className="w-5 h-5 text-gray-400" />
    }
  }

  const getStatusColor = () => {
    switch (status) {
      case 'completed':
        return 'bg-green-500'
      case 'error':
        return 'bg-red-500'
      case 'cancelled':
        return 'bg-yellow-500'
      case 'processing':
        return 'bg-blue-500'
      default:
        return 'bg-gray-500'
    }
  }

  const getStatusText = () => {
    switch (status) {
      case 'completed':
        return '转换完成'
      case 'error':
        return '转换失败'
      case 'cancelled':
        return '已取消'
      case 'processing':
        return '转换中...'
      default:
        return '等待中'
    }
  }

  return (
    <div className={`bg-gray-800 bg-opacity-50 rounded-lg p-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          {getStatusIcon()}
          <div className="flex-1 min-w-0">
            {fileName && (
              <p className="text-white font-medium truncate" title={fileName}>
                {fileName}
              </p>
            )}
            <p className="text-sm text-gray-400">{getStatusText()}</p>
          </div>
        </div>
        <div className="text-right">
          <p className="text-white font-medium">{Math.round(progress)}%</p>
          {eta && status === 'processing' && (
            <p className="text-xs text-gray-400">剩余 {eta}</p>
          )}
        </div>
      </div>

      {/* Progress Bar */}
      <div className="w-full bg-gray-700 rounded-full h-2 mb-3">
        <div
          className={`h-2 rounded-full transition-all duration-300 ${getStatusColor()}`}
          style={{ width: `${Math.min(progress, 100)}%` }}
        />
      </div>

      {/* Error Message */}
      {status === 'error' && error && (
        <div className="mb-3 p-3 bg-red-500 bg-opacity-20 border border-red-500 rounded-lg">
          <p className="text-red-300 text-sm">{error}</p>
        </div>
      )}

      {/* Detailed Information */}
      {showDetails && status === 'processing' && (
        <div className="grid grid-cols-2 gap-4 text-sm">
          {currentTime && (
            <div>
              <span className="text-gray-400">当前时间:</span>
              <span className="text-white ml-2">{currentTime}</span>
            </div>
          )}
          {totalTime && (
            <div>
              <span className="text-gray-400">总时长:</span>
              <span className="text-white ml-2">{totalTime}</span>
            </div>
          )}
          {fps && fps > 0 && (
            <div>
              <span className="text-gray-400">帧率:</span>
              <span className="text-white ml-2">{Math.round(fps)} fps</span>
            </div>
          )}
          {speed && (
            <div>
              <span className="text-gray-400">速度:</span>
              <span className="text-white ml-2">{speed}</span>
            </div>
          )}
          {bitrate && (
            <div>
              <span className="text-gray-400">码率:</span>
              <span className="text-white ml-2">{bitrate}</span>
            </div>
          )}
          {size && (
            <div>
              <span className="text-gray-400">大小:</span>
              <span className="text-white ml-2">{size}</span>
            </div>
          )}
        </div>
      )}

      {/* Completion Information */}
      {showDetails && status === 'completed' && (
        <div className="text-sm text-green-400">
          <p>✓ 转换成功完成</p>
        </div>
      )}
    </div>
  )
}

export default ProgressBar