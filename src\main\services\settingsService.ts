import * as path from 'path'
import * as os from 'os'
import { DEFAULT_PATHS } from '../../shared/constants'
import type { AppSettings } from '../../shared/types'

export class SettingsService {
  private static settings: AppSettings = {
    outputPath: path.join(os.homedir(), 'Desktop', DEFAULT_PATHS.OUTPUT_FOLDER),
    theme: 'dark',
    autoOpenOutput: true,
    maxConcurrentTasks: 3
  }

  static async getSettings(): Promise<AppSettings> {
    // TODO: Load settings from electron-store in later tasks
    return { ...this.settings }
  }

  static async updateSettings(newSettings: Partial<AppSettings>): Promise<void> {
    this.settings = { ...this.settings, ...newSettings }
    // TODO: Save settings to electron-store in later tasks
  }

  static getDefaultOutputPath(): string {
    return path.join(os.homedir(), 'Desktop', DEFAULT_PATHS.OUTPUT_FOLDER)
  }
}