import * as path from 'path'
import * as fs from 'fs'
import * as os from 'os'
import { DEFAULT_PATHS } from '../../shared/constants'

export class PathUtils {
  static ensureDirectoryExists(dirPath: string): void {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true })
    }
  }

  static getDefaultOutputDirectory(): string {
    const outputDir = path.join(os.homedir(), 'Desktop', DEFAULT_PATHS.OUTPUT_FOLDER)
    this.ensureDirectoryExists(outputDir)
    return outputDir
  }

  static getTempDirectory(): string {
    const tempDir = path.join(os.tmpdir(), DEFAULT_PATHS.TEMP_FOLDER)
    this.ensureDirectoryExists(tempDir)
    return tempDir
  }

  static generateOutputPath(inputPath: string, outputFormat: string, outputDir?: string): string {
    const filename = path.basename(inputPath, path.extname(inputPath))
    const outputDirectory = outputDir || this.getDefaultOutputDirectory()
    return path.join(outputDirectory, `${filename}.${outputFormat}`)
  }

  static isValidPath(filePath: string): boolean {
    try {
      return fs.existsSync(filePath) && fs.statSync(filePath).isFile()
    } catch {
      return false
    }
  }

  static getFileSize(filePath: string): number {
    try {
      return fs.statSync(filePath).size
    } catch {
      return 0
    }
  }
}