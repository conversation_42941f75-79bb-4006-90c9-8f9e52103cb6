import React from 'react'
import { FileType } from '../../../shared/types'

interface ConversionCardProps {
  title: string
  description: string
  fromFormat: string
  toFormat: string
  fileType: FileType
  icon: React.ReactNode
  onClick: () => void
}

const ConversionCard: React.FC<ConversionCardProps> = ({
  title,
  description,
  fromFormat,
  toFormat,
  fileType,
  icon,
  onClick
}) => {
  const getCardColor = (type: FileType) => {
    switch (type) {
      case 'VIDEO':
        return 'from-red-500 to-pink-600'
      case 'AUDIO':
        return 'from-green-500 to-teal-600'
      case 'IMAGE':
        return 'from-blue-500 to-purple-600'
      default:
        return 'from-gray-500 to-gray-600'
    }
  }

  return (
    <div 
      onClick={onClick}
      className={`
        relative overflow-hidden rounded-xl p-6 cursor-pointer
        bg-gradient-to-br ${getCardColor(fileType)}
        hover:scale-105 transform transition-all duration-400
        shadow-lg hover:shadow-glow-lg
        group border border-white border-opacity-10 hover:border-opacity-30
      `}
    >
      {/* Static background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-4 -right-4 w-16 h-16 bg-white rounded-full opacity-5 group-hover:opacity-10 transition-opacity duration-400"></div>
        <div className="absolute -bottom-4 -left-4 w-12 h-12 bg-white rounded-full opacity-5 group-hover:opacity-10 transition-opacity duration-400"></div>
      </div>

      {/* Shimmer effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white via-opacity-10 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer transition-opacity duration-400"></div>
      
      <div className="relative z-10">
        <div className="flex items-center justify-between mb-4">
          <div className="text-white text-2xl group-hover:scale-110 group-hover:rotate-12 transition-all duration-300">
            {icon}
          </div>
          <div className="text-white text-sm font-medium bg-white bg-opacity-20 px-3 py-1 rounded-full backdrop-blur-sm group-hover:bg-opacity-30 transition-all duration-300">
            {fromFormat.toUpperCase()} → {toFormat.toUpperCase()}
          </div>
        </div>
        
        <h3 className="text-white text-lg font-semibold mb-2 group-hover:text-blue-100 transition-colors duration-300">
          {title}
        </h3>
        
        <p className="text-white text-opacity-90 text-sm group-hover:text-opacity-100 transition-all duration-300">
          {description}
        </p>

        {/* Progress indicator */}
        <div className="mt-4 flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="flex-1 h-1 bg-white bg-opacity-20 rounded-full overflow-hidden">
            <div className="h-full bg-white bg-opacity-40 rounded-full animate-shimmer"></div>
          </div>
          <span className="text-white text-xs opacity-80">点击转换</span>
        </div>
      </div>
      
      {/* Animated background effect */}
      <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-5 transition-opacity duration-400" />
      
      {/* Corner accent */}
      <div className="absolute top-0 right-0 w-0 h-0 border-l-[20px] border-l-transparent border-t-[20px] border-t-white opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
    </div>
  )
}

export default ConversionCard