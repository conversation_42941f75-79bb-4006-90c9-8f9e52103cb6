import ffmpeg from 'fluent-ffmpeg'
import * as path from 'path'
import { PathUtils } from '../utils/pathUtils'
import { FFmpegManager } from '../utils/ffmpegManager'
import { ProgressManager } from '../utils/progressManager'
import { ErrorHandler } from '../utils/errorHandler'
import { ConversionError, ErrorFactory, ErrorType } from '../../shared/errors'
import type { ConversionOptions } from '../../shared/types'

export class VideoConverter {
  private static initialized = false

  static async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      const { ffmpegPath, ffprobePath } = await FFmpegManager.initialize()
      
      if (ffmpegPath && ffprobePath) {
        ffmpeg.setFfmpegPath(ffmpegPath)
        ffmpeg.setFfprobePath(ffprobePath)
        this.initialized = true
        console.log('VideoConverter initialized with FFmpeg')
      } else {
        const error = ErrorFactory.ffmpegNotFound()
        await ErrorHandler.getInstance().handleError(error)
        throw error
      }
    } catch (error) {
      console.error('Failed to initialize VideoConverter:', error)
      if (!(error instanceof ConversionError)) {
        const conversionError = ErrorFactory.fromNativeError(error as Error, ErrorType.INITIALIZATION_FAILED)
        await ErrorHandler.getInstance().handleError(conversionError)
        throw conversionError
      }
      throw error
    }
  }

  static async convert(
    inputPath: string,
    outputPath: string,
    _inputFormat: string,
    outputFormat: string,
    options?: ConversionOptions,
    progressId?: string
  ): Promise<void> {
    const errorHandler = ErrorHandler.getInstance()

    // 检查初始化状态
    if (!this.initialized || !FFmpegManager.isAvailable()) {
      const error = ErrorFactory.ffmpegNotFound()
      await errorHandler.handleError(error, { inputPath, outputPath, outputFormat })
      throw error
    }

    // 验证输入文件
    if (!PathUtils.isValidPath(inputPath)) {
      const error = ErrorFactory.fileNotFound(inputPath)
      await errorHandler.handleError(error, { outputPath, outputFormat })
      throw error
    }

    // 验证输出目录
    try {
      const outputDir = path.dirname(outputPath)
      PathUtils.ensureDirectoryExists(outputDir)
    } catch (err) {
      const error = ErrorFactory.fileAccessDenied(path.dirname(outputPath))
      await errorHandler.handleError(error, { inputPath, outputPath })
      throw error
    }

    const progressManager = ProgressManager.getInstance()

    return new Promise((resolve, reject) => {
      let command = ffmpeg(inputPath)

      try {
        // Apply video options with validation
        if (options?.resolution) {
          if (!/^\d+x\d+$/.test(options.resolution)) {
            const error = new ConversionError(
              ErrorType.INVALID_CONVERSION_OPTIONS,
              `Invalid resolution format: ${options.resolution}`,
              { details: { resolution: options.resolution } }
            )
            errorHandler.handleError(error, { inputPath, outputPath, options })
            reject(error)
            return
          }
          command = command.size(options.resolution)
        }

        if (options?.bitrate) {
          command = command.videoBitrate(options.bitrate)
        }

        if (options?.framerate) {
          if (typeof options.framerate === 'number' && options.framerate > 0) {
            command = command.fps(options.framerate)
          }
        }

        // Apply audio options if present
        if (options?.audioBitrate) {
          command = command.audioBitrate(options.audioBitrate)
        }

        if (options?.sampleRate) {
          if (typeof options.sampleRate === 'number' && options.sampleRate > 0) {
            command = command.audioFrequency(options.sampleRate)
          }
        }

        if (options?.channels) {
          if (typeof options.channels === 'number' && options.channels > 0) {
            command = command.audioChannels(options.channels)
          }
        }

        // Set output format and codec based on format
        command = command.format(outputFormat)
        
        // Apply format-specific codec settings
        if (outputFormat.toLowerCase() === 'mkv') {
          // For MKV, use H.264 video codec and AAC audio codec for better compatibility
          command = command.videoCodec('libx264').audioCodec('aac')
        }

        // Handle progress
        command.on('progress', (progress) => {
          if (progressId) {
            const progressPercent = Math.round(progress.percent || 0)
            
            // Extract additional progress information
            const currentTime = progress.timemark || '00:00:00'
            const fps = progress.currentFps || 0
            const speed = progress.currentKbps ? `${Math.round(progress.currentKbps)}kbps` : undefined
            const size = progress.targetSize ? ProgressManager.formatFileSize(progress.targetSize * 1024) : undefined

            progressManager.updateProgress(progressId, {
              progress: progressPercent,
              currentTime,
              fps,
              speed,
              size,
              eta: progress.percent ? ProgressManager.formatTime((100 - progress.percent) * (Date.now() - (progressManager.getConversion(progressId)?.startTime || Date.now())) / (progress.percent * 1000)) : undefined
            })
          }
        })

        // Handle completion
        command.on('end', () => {
          console.log(`Video conversion completed: ${inputPath} -> ${outputPath}`)
          if (progressId) {
            progressManager.completeConversion(progressId)
          }
          resolve()
        })

        // Handle errors with detailed error processing
        command.on('error', async (err) => {
          console.error('Video conversion error:', err)
          
          // Create detailed conversion error
          const conversionError = ErrorFactory.conversionFailed(err.message, {
            inputPath,
            outputPath,
            outputFormat,
            options,
            ffmpegError: err
          })

          // Handle the error
          await errorHandler.handleError(conversionError, {
            converter: 'VideoConverter',
            inputPath,
            outputPath,
            options
          })

          if (progressId) {
            progressManager.errorConversion(progressId, conversionError.message)
          }

          reject(conversionError)
        })

        // Start conversion
        if (progressId) {
          progressManager.startConversion(progressId)
        }
        command.save(outputPath)

      } catch (setupError) {
        // Handle setup errors
        const error = ErrorFactory.fromNativeError(setupError as Error, ErrorType.INVALID_CONVERSION_OPTIONS)
        errorHandler.handleError(error, { inputPath, outputPath, options })
        reject(error)
      }
    })
  }

  static getSupportedFormats(): { input: string[], output: string[] } {
    // Base formats that are commonly supported
    const baseInput = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', '3gp', 'asf']
    const baseOutput = ['mp4', 'avi', 'mov', 'flv', 'webm', 'ogv']
    
    // Add MKV only if FFmpeg supports it (will be checked dynamically)
    // For now, we'll exclude MKV and WMV from the default list due to compatibility issues
    return {
      input: [...baseInput, 'mkv'], // Keep MKV for input as it's usually supported for reading
      output: baseOutput // Remove MKV and WMV from output until we can verify support
    }
  }

  static async checkFormatSupport(format: string): Promise<boolean> {
    if (!this.initialized || !FFmpegManager.isAvailable()) {
      return false
    }

    return new Promise((resolve) => {
      const { spawn } = require('child_process')
      const ffmpegPath = FFmpegManager.getFFmpegPath()
      
      if (!ffmpegPath) {
        resolve(false)
        return
      }

      const child = spawn(ffmpegPath, ['-formats'], { 
        stdio: 'pipe',
        timeout: 5000 
      })
      
      let output = ''
      child.stdout.on('data', (data) => {
        output += data.toString()
      })
      
      child.on('error', () => resolve(false))
      child.on('exit', (code) => {
        if (code === 0) {
          // Check if the format is listed in the output
          const lines = output.split('\n')
          const formatSupported = lines.some(line => 
            line.includes(format) && (line.includes('E') || line.includes('DE'))
          )
          resolve(formatSupported)
        } else {
          resolve(false)
        }
      })
      
      // Timeout fallback
      setTimeout(() => {
        child.kill()
        resolve(false)
      }, 5000)
    })
  }

  static async getVideoInfo(inputPath: string): Promise<any> {
    if (!this.initialized || !FFmpegManager.isAvailable()) {
      throw new Error('FFmpeg not available')
    }

    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(inputPath, (err, metadata) => {
        if (err) {
          reject(new Error(`Failed to get video info: ${err.message}`))
        } else {
          resolve(metadata)
        }
      })
    })
  }
}