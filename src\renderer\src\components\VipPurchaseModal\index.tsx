import React, { useState, useEffect, useRef } from 'react';
import { Modal, Card, message, Spin, QRCode } from 'antd';
import { getBuyVipInfo, getVipOrder, getVipOrderInfo, getUserInfo, VipPackage } from '@renderer/api/http';
import WxPayIcon from '@renderer/assets/icon/wxpay.png';
import AliPayIcon from '@renderer/assets/icon/alipay.png';
import { trackVipPurchase } from '@renderer/services/analytics-service';
import TrialService from '@renderer/services/trial-service';

interface VipPurchaseModalProps {
    visible: boolean;
    onCancel: () => void;
    onPurchaseSuccess?: () => void;
}

const VipPurchaseModal: React.FC<VipPurchaseModalProps> = ({
    visible,
    onCancel,
    onPurchaseSuccess
}) => {
    const [loading, setLoading] = useState<boolean>(false);
    const [vipPackages, setVipPackages] = useState<VipPackage[]>([]);
    const [selectedPackage, setSelectedPackage] = useState<number | null>(null);
    const [paymentMethod, setPaymentMethod] = useState<string>('wechat');
    const [orderLoading, setOrderLoading] = useState<boolean>(false);
    const [qrData, setQrData] = useState<string>('');
    const [orderId, setOrderId] = useState<number | null>(null);
    const [remainSec, setRemainSec] = useState<number>(300);
    const pollTimer = useRef<NodeJS.Timeout | null>(null);
    const tickTimer = useRef<NodeJS.Timeout | null>(null);

    // 获取VIP套餐信息
    const fetchVipPackages = async () => {
        try {
            setLoading(true);
            const response = await getBuyVipInfo();
            if (response.code === 200 && response.data?.list) {
                // 排序：将标题包含“永久”的套餐置顶
                const sorted = [...response.data.list].sort((a, b) => {
                    const aPerm = /永久/.test(a.title);
                    const bPerm = /永久/.test(b.title);
                    if (aPerm === bPerm) return 0;
                    return aPerm ? -1 : 1;
                });
                setVipPackages(sorted);
                // 默认选择：优先选择“永久会员”，否则第一个
                if (sorted.length > 0) {
                    const perm = sorted.find(p => /永久/.test(p.title));
                    setSelectedPackage((perm || sorted[0]).id);
                }
            } else {
                message.error('获取VIP套餐信息失败');
            }
        } catch (error) {
            console.error('获取VIP套餐信息失败:', error);
            message.error('获取VIP套餐信息失败，请重试');
        } finally {
            setLoading(false);
        }
    };

    // 弹窗打开时获取套餐信息
    useEffect(() => {
        if (visible) {
            fetchVipPackages();
        } else {
            // 弹窗关闭时重置状态
            setSelectedPackage(null);
            setPaymentMethod('wechat');
            setQrData('');
            clearTimers();
        }
    }, [visible]);

    // 处理套餐选择
    const handlePackageSelect = (packageId: number) => {
        // 若点击的是当前已选套餐，则不做处理，避免清空二维码
        if (packageId === selectedPackage) return;
        setSelectedPackage(packageId);
        setQrData('');
    };

    // 处理购买（带支付方式）
    const handlePurchaseWith = async (method: 'wechat' | 'alipay') => {
        if (!selectedPackage) {
            message.warning('请选择一个VIP套餐');
            return;
        }

        const selectedPkg = vipPackages.find(pkg => pkg.id === selectedPackage);
        if (!selectedPkg) {
            message.error('套餐信息错误');
            return;
        }

        try {
            setOrderLoading(true);
            setQrData('');
            // 映射支付方式：微信 -> wxpayqrcode，支付宝 -> alipay
            const payment = method === 'wechat' ? 'wxpayqrcode' : 'alipay';
            const res = await getVipOrder(selectedPkg.id, payment);
            if (res.code === 200 && res.data?.paydata && res.data?.order?.order_id) {
                setQrData(res.data.paydata);
                setOrderId(res.data.order.order_id);
                // 埋点：订单创建
                trackVipPurchase('order_created', method, res.data.order.order_id);
                // 启动轮询和倒计时
                startPolling(res.data.order.order_id);
            } else {
                message.error(res.message || '获取支付信息失败');
                trackVipPurchase('fail', method);
            }
        } catch (err) {
            console.error('getVipOrder error:', err);
            message.error('创建订单失败，请稍后重试');
            trackVipPurchase('fail', method);
        } finally {
            setOrderLoading(false);
        }
    };

    // 点击支付方式即下单并展示二维码
    const handleSelectPaymentAndPurchase = (method: 'wechat' | 'alipay') => {
        if (!selectedPackage) {
            message.warning('请选择一个VIP套餐');
            return;
        }
        setPaymentMethod(method);
        // 初始化倒计时并直接下单，二维码在页面内展示
        setRemainSec(300);
        clearTimers();
        void handlePurchaseWith(method);
    };

    // 启动轮询与倒计时
    const startPolling = (oid: number) => {
        clearTimers();
        // 每2秒轮询
        pollTimer.current = setInterval(async () => {
            try {
                const r = await getVipOrderInfo(oid);
                if (r.code === 200 && r.data?.pay_status === 'SUCCESS') {
                    message.success('支付成功');
                    // 刷新用户信息并回调
                    await safeRefreshUserInfo();
                    // 埋点：支付成功
                    trackVipPurchase('success', paymentMethod, oid);
                    // 清理状态
                    clearTimers();
                    setQrData('');
                    setOrderId(null);
                    setRemainSec(300);
                    if (onPurchaseSuccess) onPurchaseSuccess();
                }
            } catch (e) {
                // 静默失败，继续轮询
            }
        }, 2000);

        // 每秒倒计时
        tickTimer.current = setInterval(() => {
            setRemainSec(prev => {
                if (prev <= 1) {
                    clearTimers();
                    message.warning('二维码已过期，请重新发起支付');
                    // 埋点：超时失败
                    trackVipPurchase('fail', paymentMethod, orderId || undefined);
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);
    };

    const clearTimers = () => {
        if (pollTimer.current) { clearInterval(pollTimer.current); pollTimer.current = null; }
        if (tickTimer.current) { clearInterval(tickTimer.current); tickTimer.current = null; }
    };

    const safeRefreshUserInfo = async () => {
        try {
            const userInfoResponse = await getUserInfo();
            if (userInfoResponse.code === 200 && userInfoResponse.data?.user) {
                const token = localStorage.getItem('AUTH_TOKEN') || localStorage.getItem('userToken') || '';
                const formattedUserInfo = {
                    token,
                    nickname: userInfoResponse.data.user.nickname,
                    avatar: userInfoResponse.data.user.avatar,
                    is_vip: userInfoResponse.data.user.is_vip,
                    vip_status: userInfoResponse.data.user.vip_status,
                    vip: userInfoResponse.data.vip
                };
                localStorage.setItem('userInfo', JSON.stringify(formattedUserInfo));
                
                // 同步VIP状态到试用服务
                const trialService = TrialService.getInstance();
                trialService.syncUserVipStatus();
                
                // 同步 VIP 状态到主进程
                try {
                    // await window.api.setVipMode(formattedUserInfo.is_vip === 1);
                } catch { /* noop */ }
            }
        } catch {
            // ignore
        }
    };

    // 主弹窗关闭时清理
    useEffect(() => {
        if (!visible) return;
        return () => { /* noop */ };
    }, [visible]);

    // 当选中套餐变化或弹窗刚打开时，自动生成/刷新二维码（支付方式保持当前选中，默认微信）
    useEffect(() => {
        if (!visible) return;
        if (!selectedPackage) return;
        setRemainSec(300);
        clearTimers();
        void handlePurchaseWith(paymentMethod as 'wechat' | 'alipay');
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedPackage, visible]);

    // 获取套餐类型对应的标签
    const getPackageTypeLabel = (type: number) => {
        switch (type) {
            case 2: return '体验版';
            case 3: return '热门';
            case 4: return '折扣';
            case 5: return '推荐';
            default: return '';
        }
    };

    // 获取套餐类型对应的颜色
    const getPackageTypeColor = (type: number) => {
        switch (type) {
            case 2: return '#52c41a';
            case 3: return '#fa541c';
            case 4: return '#1890ff';
            case 5: return '#722ed1';
            default: return '#666';
        }
    };

    return (
        <>
            <Modal
                title="开通VIP会员"
                open={visible}
                onCancel={async () => {
                    // 关闭时刷新并同步用户信息
                    await safeRefreshUserInfo();
                    
                    // 同步VIP状态到试用服务
                    const trialService = TrialService.getInstance();
                    trialService.syncUserVipStatus();
                    
                    try {
                        const raw = localStorage.getItem('userInfo');
                        const info = raw ? JSON.parse(raw) : null;
                        // window.api.setVipMode(info?.is_vip === 1);
                    } catch { /* noop */ }
                    // 埋点：关闭/取消
                    trackVipPurchase('close', paymentMethod, orderId || undefined);
                    // 清理与复位
                    clearTimers();
                    setOrderId(null);
                    setQrData('');
                    setRemainSec(300);
                    // 调用外部关闭
                    onCancel();
                }}
                footer={null}
                width={600}
                centered
                maskClosable={false}
            >
                {loading ? (
                    <div className="flex justify-center items-center py-12">
                        <Spin size="large" />
                        <span className="ml-3 text-gray-600">正在加载套餐信息...</span>
                    </div>
                ) : (
                    <div className="py-4">
                        {/* VIP套餐列表 */}
                        <div className="mb-4">
                            <div className="grid grid-cols-2 gap-3">
                                {vipPackages.map((pkg) => (
                                    <Card
                                        key={pkg.id}
                                        className={`cursor-pointer transition-all duration-200 hover:shadow-md ${selectedPackage === pkg.id
                                            ? 'border-2 border-blue-500 shadow-md'
                                            : 'border-2 border-gray-200'
                                            }`}
                                        onClick={() => handlePackageSelect(pkg.id)}
                                        bodyStyle={{ padding: '12px' }}
                                    >
                                        <div className="text-center">
                                            {/* 套餐标签 */}
                                            {getPackageTypeLabel(pkg.type) && (
                                                <div
                                                    className="inline-block px-2 py-0.5 text-[11px] text-white rounded mb-1"
                                                    style={{ backgroundColor: getPackageTypeColor(pkg.type) }}
                                                >
                                                    {getPackageTypeLabel(pkg.type)}
                                                </div>
                                            )}

                                            {/* 套餐标题 */}
                                            <h4 className="text-sm font-medium mb-1">{pkg.title}</h4>

                                            {/* 价格信息 */}
                                            <div className="mb-1">
                                                <span className="text-xl font-bold text-red-500">
                                                    ¥{pkg.amount}
                                                </span>
                                            </div>

                                            {/* 原价 */}
                                            <div className="text-gray-400">
                                                <span className="line-through text-xs">原价 ¥{pkg.amount_original}</span>
                                            </div>
                                        </div>
                                    </Card>
                                ))}
                            </div>
                        </div>

                        {/* 支付方式选择（标题与按钮同排） */}
                        <div className="mb-3">
                            <div className="flex items-center">
                                <h3 className="text-base font-medium mr-3">选择支付方式：</h3>
                                <div className="flex space-x-3">
                                    {/* 微信支付按钮 */}
                                    <div
                                        className={`h-10 px-4 flex items-center justify-center space-x-2 rounded-md cursor-pointer transition-all duration-200 border
                                            ${paymentMethod === 'wechat' ? 'border-red-500 text-red-500 bg-white' : 'border-gray-300 text-gray-700 bg-white hover:border-gray-400'}`}
                                        onClick={() => handleSelectPaymentAndPurchase('wechat')}
                                    >
                                        <img src={WxPayIcon} alt="微信支付" className="w-5 h-5" />
                                        <span className="font-medium text-sm">微信支付</span>
                                    </div>

                                    {/* 支付宝按钮 */}
                                    <div
                                        className={`h-10 px-4 flex items-center justify-center space-x-2 rounded-md cursor-pointer transition-all duration-200 border
                                            ${paymentMethod === 'alipay' ? 'border-red-500 text-red-500 bg-white' : 'border-gray-300 text-gray-700 bg-white hover:border-gray-400'}`}
                                        onClick={() => handleSelectPaymentAndPurchase('alipay')}
                                    >
                                        <img src={AliPayIcon} alt="支付宝" className="w-5 h-5" />
                                        <span className="font-medium text-sm">支付宝</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* 支付二维码区域（内嵌展示） */}
                        <div className="mt-2 border rounded-md p-3 bg-gray-50">
                            {orderLoading ? (
                                <div className="flex justify-center items-center" style={{ height: '260px' }}>
                                    <Spin />
                                    <span className="ml-2 text-gray-600">正在创建订单...</span>
                                </div>
                            ) : (
                                <div className="flex flex-col items-center justify-center" style={{ minHeight: '260px' }}>
                                    <div className="mb-2 text-sm text-gray-600">
                                        使用{paymentMethod === 'wechat' ? '微信' : '支付宝'}扫码支付
                                    </div>
                                    {qrData ? (
                                        <>
                                            <QRCode value={qrData} size={200} />
                                            <div className="mt-3 text-xs text-gray-500">{remainSec > 0 ? `二维码${remainSec}s后过期` : '二维码已过期'}</div>
                                        </>
                                    ) : (
                                        <div className="flex items-center justify-center" style={{ height: '200px' }}>
                                            <div className="text-gray-400">暂无二维码</div>
                                        </div>
                                    )}
                                </div>
                            )}
                        </div>
                    </div>
                )}
            </Modal>
        </>
    );
};

export default VipPurchaseModal;