import React, { useState, useEffect } from 'react'
import { X, Trash2, <PERSON>Circle, AlertCircle } from 'lucide-react'
import ProgressBar from './ProgressBar'

interface ConversionProgress {
  id: string
  fileName: string
  inputPath: string
  outputPath: string
  status: 'idle' | 'processing' | 'completed' | 'error' | 'cancelled'
  progress: number
  currentTime?: string
  totalTime?: string
  speed?: string
  fps?: number
  bitrate?: string
  size?: string
  eta?: string
  error?: string
  startTime: number
  endTime?: number
}

interface ConversionProgressProps {
  isVisible: boolean
  onClose: () => void
}

const ConversionProgressComponent: React.FC<ConversionProgressProps> = ({
  isVisible,
  onClose
}) => {
  const [conversions, setConversions] = useState<ConversionProgress[]>([])
  const [activeCount, setActiveCount] = useState(0)

  useEffect(() => {
    if (isVisible) {
      loadConversions()
      const interval = setInterval(loadConversions, 1000) // Update every second
      return () => clearInterval(interval)
    }
    return undefined
  }, [isVisible])

  useEffect(() => {
    // Listen for progress updates
    const handleProgressUpdate = (_event: any, conversion: ConversionProgress) => {
      setConversions(prev => {
        const index = prev.findIndex(c => c.id === conversion.id)
        if (index >= 0) {
          const updated = [...prev]
          updated[index] = conversion
          return updated
        } else {
          return [...prev, conversion]
        }
      })
    }

    const handleConversionRemoved = (_event: any, conversionId: string) => {
      setConversions(prev => prev.filter(c => c.id !== conversionId))
    }

    const handleBulkRemoved = (_event: any, conversionIds: string[]) => {
      setConversions(prev => prev.filter(c => !conversionIds.includes(c.id)))
    }

    // Add event listeners
    if (window.electron?.ipcRenderer) {
      window.electron.ipcRenderer.on('conversion-progress-update', handleProgressUpdate)
      window.electron.ipcRenderer.on('conversion-removed', handleConversionRemoved)
      window.electron.ipcRenderer.on('conversions-bulk-removed', handleBulkRemoved)

      return () => {
        window.electron.ipcRenderer.removeListener('conversion-progress-update', handleProgressUpdate)
        window.electron.ipcRenderer.removeListener('conversion-removed', handleConversionRemoved)
        window.electron.ipcRenderer.removeListener('conversions-bulk-removed', handleBulkRemoved)
      }
    }
    return undefined
  }, [])

  const loadConversions = async () => {
    try {
      const allConversions = await window.electron.ipcRenderer.invoke('get-all-conversions')
      setConversions(allConversions)
      setActiveCount(allConversions.filter((c: ConversionProgress) => c.status === 'processing').length)
    } catch (error) {
      console.error('Failed to load conversions:', error)
    }
  }

  const handleRemoveConversion = async (conversionId: string) => {
    try {
      await window.electron.ipcRenderer.invoke('remove-conversion', conversionId)
      setConversions(prev => prev.filter(c => c.id !== conversionId))
    } catch (error) {
      console.error('Failed to remove conversion:', error)
    }
  }

  const handleClearCompleted = async () => {
    try {
      await window.electron.ipcRenderer.invoke('clear-completed-conversions')
      setConversions(prev => prev.filter(c => c.status === 'processing'))
    } catch (error) {
      console.error('Failed to clear completed conversions:', error)
    }
  }

  const formatDuration = (startTime: number, endTime?: number): string => {
    const duration = (endTime || Date.now()) - startTime
    const seconds = Math.floor(duration / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)

    if (hours > 0) {
      return `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`
    }
    return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`
  }

  const completedCount = conversions.filter(c => c.status === 'completed').length
  const errorCount = conversions.filter(c => c.status === 'error').length

  if (!isVisible) return null

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-gray-900 rounded-lg w-full max-w-4xl max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center space-x-4">
            <h2 className="text-xl font-semibold text-white">转换进度</h2>
            <div className="flex items-center space-x-4 text-sm">
              {activeCount > 0 && (
                <div className="flex items-center space-x-1 text-blue-400">
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
                  <span>{activeCount} 进行中</span>
                </div>
              )}
              {completedCount > 0 && (
                <div className="flex items-center space-x-1 text-green-400">
                  <CheckCircle className="w-4 h-4" />
                  <span>{completedCount} 已完成</span>
                </div>
              )}
              {errorCount > 0 && (
                <div className="flex items-center space-x-1 text-red-400">
                  <AlertCircle className="w-4 h-4" />
                  <span>{errorCount} 失败</span>
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {(completedCount > 0 || errorCount > 0) && (
              <button
                onClick={handleClearCompleted}
                className="flex items-center space-x-2 px-3 py-1 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                title="清除已完成的转换"
              >
                <Trash2 className="w-4 h-4" />
                <span>清除已完成</span>
              </button>
            )}
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-700 text-white rounded-lg transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {conversions.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 text-lg mb-2">暂无转换任务</div>
              <div className="text-gray-500 text-sm">开始转换文件后，进度将在这里显示</div>
            </div>
          ) : (
            <div className="space-y-4">
              {conversions.map((conversion) => (
                <div key={conversion.id} className="relative">
                  <ProgressBar
                    progress={conversion.progress}
                    status={conversion.status}
                    fileName={conversion.fileName}
                    currentTime={conversion.currentTime}
                    totalTime={conversion.totalTime}
                    speed={conversion.speed}
                    fps={conversion.fps}
                    bitrate={conversion.bitrate}
                    size={conversion.size}
                    eta={conversion.eta}
                    error={conversion.error}
                    showDetails={true}
                  />
                  
                  {/* Additional Info */}
                  <div className="mt-2 flex items-center justify-between text-xs text-gray-400">
                    <div>
                      <span>开始时间: {new Date(conversion.startTime).toLocaleTimeString()}</span>
                      {conversion.endTime && (
                        <span className="ml-4">
                          耗时: {formatDuration(conversion.startTime, conversion.endTime)}
                        </span>
                      )}
                    </div>
                    {conversion.status !== 'processing' && (
                      <button
                        onClick={() => handleRemoveConversion(conversion.id)}
                        className="p-1 hover:bg-gray-700 text-gray-400 hover:text-white rounded transition-colors"
                        title="移除此任务"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ConversionProgressComponent