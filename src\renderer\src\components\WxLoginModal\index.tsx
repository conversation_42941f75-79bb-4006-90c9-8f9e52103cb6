import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>dal, QRC<PERSON>, Button, message, Spin } from 'antd';
import { getWxAuthUrl, checkWxAuthStatus, getUserInfo, WxAuthInitResp, WxAuthStatusResp } from '@renderer/api/http';

interface WxLoginModalProps {
  visible: boolean;
  onCancel: () => void;
  onLoginSuccess: (userinfo: any) => void;
}

const WxLoginModal: React.FC<WxLoginModalProps> = ({ visible, onCancel, onLoginSuccess }) => {
  // 配置与本地存储 key
  const QR_VALID_SECONDS = 300; // 5 分钟
  const LS_QR_URL = 'WX_QR_URL';
  const LS_QR_TOKEN = 'WX_QR_TOKEN';
  const LS_QR_EXPIRES_AT = 'WX_QR_EXPIRES_AT';

  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [expired, setExpired] = useState<boolean>(false);
  const [countdown, setCountdown] = useState<number>(300); // 5分钟倒计时

  const tokenRef = useRef<string>('');
  const pollingTimerRef = useRef<NodeJS.Timeout | null>(null);
  const countdownTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 保存二维码信息到本地
  const saveQrToStorage = (url: string, token: string, expiresAt: number) => {
    localStorage.setItem(LS_QR_URL, url);
    localStorage.setItem(LS_QR_TOKEN, token);
    localStorage.setItem(LS_QR_EXPIRES_AT, String(expiresAt));
  };

  // 移除本地二维码信息
  const clearQrFromStorage = () => {
    localStorage.removeItem(LS_QR_URL);
    localStorage.removeItem(LS_QR_TOKEN);
    localStorage.removeItem(LS_QR_EXPIRES_AT);
  };

  // 从本地加载未过期二维码，返回是否加载成功
  const loadExistingQRCodeIfValid = () => {
    const url = localStorage.getItem(LS_QR_URL) || '';
    const token = localStorage.getItem(LS_QR_TOKEN) || '';
    const expiresAtStr = localStorage.getItem(LS_QR_EXPIRES_AT) || '';
    const now = Date.now();
    const expiresAt = expiresAtStr ? parseInt(expiresAtStr, 10) : 0;
    const remaining = Math.floor((expiresAt - now) / 1000);
    if (url && token && expiresAt > now && remaining > 0) {
      setQrCodeUrl(url);
      tokenRef.current = token;
      setExpired(false);
      setCountdown(Math.min(remaining, QR_VALID_SECONDS));
      // 重启轮询和倒计时
      startPolling();
      startCountdown();
      return true;
    }
    return false;
  };

  // 初始化获取二维码
  const initQRCode = async () => {
    try {
      setLoading(true);
      setExpired(false);
      setCountdown(QR_VALID_SECONDS);

      const response = await getWxAuthUrl();
      if (response.code === 200 && response.data) {
        const { url, token } = response.data;
        setQrCodeUrl(url);
        tokenRef.current = token;
        // 存储到本地，便于关闭再打开时复用
        const expiresAt = Date.now() + QR_VALID_SECONDS * 1000;
        saveQrToStorage(url, token, expiresAt);

        // 开始轮询
        startPolling();
        // 开始倒计时
        startCountdown();
      } else {
        message.error('获取二维码失败，请重试');
      }
    } catch (error) {
      console.error('获取二维码失败:', error);
      message.error('获取二维码失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 开始轮询检查登录状态
  const startPolling = () => {
    // 清除之前的定时器
    if (pollingTimerRef.current) {
      clearInterval(pollingTimerRef.current);
    }

    pollingTimerRef.current = setInterval(async () => {
      try {
        const response = await checkWxAuthStatus(tokenRef.current);
        if (response.code === 200 && response.data) {
          const { flag, userinfo } = response.data;
          if (flag && userinfo) {
            // 登录成功，先存储 token
            clearAllTimers();
            // 登录成功后清理本地二维码缓存
            clearQrFromStorage();

            // 设置 token 到 localStorage
            localStorage.setItem('userToken', userinfo.token);
            localStorage.setItem('AUTH_TOKEN', userinfo.token);

            try {
              // 调用用户信息接口获取完整的用户信息
              const userInfoResponse = await getUserInfo();
              if (userInfoResponse.code === 200 && userInfoResponse.data?.user) {
                // 构建与 TitleBar 中一致的用户信息格式
                const formattedUserInfo = {
                  token: userinfo.token,
                  nickname: userInfoResponse.data.user.nickname,
                  avatar: userInfoResponse.data.user.avatar,
                  is_vip: userInfoResponse.data.user.is_vip,
                  vip_status: userInfoResponse.data.user.vip_status,
                  vip: userInfoResponse.data.vip // 添加 VIP 信息
                };

                // 存储格式化后的用户信息
                localStorage.setItem('userInfo', JSON.stringify(formattedUserInfo));

                message.success('登录成功');
                onLoginSuccess(formattedUserInfo);
                onCancel();
              } else {
                // 用户信息获取失败，使用微信返回的基本信息
                console.warn('获取用户详细信息失败，使用微信基本信息');
                localStorage.setItem('userInfo', JSON.stringify(userinfo));
                message.success('登录成功');
                onLoginSuccess(userinfo);
                onCancel();
              }
            } catch (error) {
              console.error('获取用户信息失败:', error);
              // 如果用户信息接口调用失败，使用微信返回的基本信息
              localStorage.setItem('userInfo', JSON.stringify(userinfo));
              message.success('登录成功');
              onLoginSuccess(userinfo);
              onCancel();
            }
          }
        }
      } catch (error) {
        console.error('轮询登录状态失败:', error);
      }
    }, 2000); // 每2秒轮询一次
  };

  // 开始倒计时
  const startCountdown = () => {
    // 清除之前的定时器
    if (countdownTimerRef.current) {
      clearInterval(countdownTimerRef.current);
    }

    countdownTimerRef.current = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          // 倒计时结束，二维码过期
          setExpired(true);
          clearAllTimers();
          // 到期后清理本地二维码缓存
          clearQrFromStorage();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // 清除所有定时器
  const clearAllTimers = () => {
    if (pollingTimerRef.current) {
      clearInterval(pollingTimerRef.current);
      pollingTimerRef.current = null;
    }
    if (countdownTimerRef.current) {
      clearInterval(countdownTimerRef.current);
      countdownTimerRef.current = null;
    }
  };

  // 刷新二维码
  const refreshQRCode = () => {
    // 若存在未过期二维码，则不允许刷新；若当前无二维码（获取失败或已清空），允许重新获取
    if (!expired && qrCodeUrl) {
      message.warning('二维码未过期，无需刷新');
      return;
    }
    initQRCode();
  };

  // 格式化倒计时显示
  const formatCountdown = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 弹窗打开时初始化二维码
  useEffect(() => {
    if (visible) {
      // 优先复用本地未过期二维码，否则重新获取
      const reused = loadExistingQRCodeIfValid();
      if (!reused) {
        initQRCode();
      }
    } else {
      // 弹窗关闭时清除定时器
      clearAllTimers();
      setQrCodeUrl('');
      setExpired(false);
      setCountdown(QR_VALID_SECONDS);
    }

    return () => {
      clearAllTimers();
    };
  }, [visible]);

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      clearAllTimers();
    };
  }, []);

  return (
    <Modal
      title="微信扫码登录"
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={400}
      centered
      maskClosable={false}
    >
      <div className="flex flex-col items-center py-6">
        {loading ? (
          <div className="flex flex-col items-center">
            <Spin size="large" />
            <p className="mt-4 text-gray-600">正在生成二维码...</p>
          </div>
        ) : expired ? (
          <div className="flex flex-col items-center">
            <div className="w-48 h-48 bg-gray-100 flex items-center justify-center rounded-lg">
              <p className="text-gray-500">二维码已过期</p>
            </div>
            <Button
              type="primary"
              onClick={refreshQRCode}
              className="mt-4"
            >
              刷新二维码
            </Button>
          </div>
        ) : qrCodeUrl ? (
          <div className="flex flex-col items-center">
            <QRCode
              value={qrCodeUrl}
              size={192}
              errorLevel="H"
            />
            <div className="mt-4 text-center">
              <p className="text-gray-700 font-medium">请使用微信扫码登录</p>
              <p className="text-gray-500 text-sm mt-1">
                二维码有效期: {formatCountdown(countdown)}
              </p>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center">
            <p className="text-red-500">获取二维码失败</p>
            <Button
              type="primary"
              onClick={refreshQRCode}
              className="mt-4"
            >
              重新获取
            </Button>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default WxLoginModal;