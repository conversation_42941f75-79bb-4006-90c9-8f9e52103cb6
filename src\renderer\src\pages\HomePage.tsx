import React, { useState, useEffect } from 'react'
import ConversionPage from './ConversionPage'
import SettingsPage from './SettingsPage'
import { FileVideo, FileAudio, FileImage, Play, Music, Camera, Film, Headphones, ImageIcon, Minimize2, Share2, Eye, Zap, Printer, Smartphone, Globe, Monitor, Star, Radio, Settings, Lock, Crown } from 'lucide-react'
import { useTheme } from '../contexts/ThemeContext'
import type { FileType, TrialCategory } from '../../../shared/types'
import Logo from '../assets/logo.png'
import { trackMenuClick } from '@renderer/services/analytics-service'
import TrialService from '../services/trial-service'

interface CategoryItem {
  id: string
  name: string
  icon: React.ReactNode
  color: string
  bgColor: string
  description: string
  fromFormat: string
  toFormat: string
  fileType: FileType
}

interface Category {
  id: string
  name: string
  icon: React.ReactNode
  color: string
  items: CategoryItem[]
}

const HomePage: React.FC = () => {
  const { theme } = useTheme()
  const [currentPage, setCurrentPage] = useState<'home' | 'conversion' | 'settings'>('home')
  const [selectedConversion, setSelectedConversion] = useState<{
    from: string
    to: string
    fileType: FileType
  } | null>(null)
  const [trialService] = useState(() => TrialService.getInstance())
  const [isVip, setIsVip] = useState(false)
  const [trialStatus, setTrialStatus] = useState({ VIDEO: false, AUDIO: false, IMAGE: false })
  

  useEffect(() => {
    const updateTrialState = () => {
      const state = trialService.getTrialState()
      setIsVip(state.isVip)
      setTrialStatus(state.trialStatus)
    }

    // Force refresh trial state to ensure VIP status is properly applied
    trialService.forceRefreshTrialState()
    updateTrialState()

    // Listen for trial state changes
    const handleStorageChange = () => {
      updateTrialState()
    }

    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [trialService])

  const categories: Category[] = [
    {
      id: 'video',
      name: '视频转换',
      icon: <FileVideo className="w-8 h-8" />,
      color: 'text-red-400',
      items: [
        {
          id: 'video-mobile',
          name: '视频转MP4',
          icon: <Smartphone className="w-6 h-6" />,
          color: 'text-red-400',
          bgColor: 'bg-red-500',
          description: '适合手机、平板等设备播放',
          fromFormat: '',
          toFormat: 'mp4',
          fileType: 'VIDEO'
        },
        {
          id: 'video-web',
          name: '视频转WEBM',
          icon: <Globe className="w-6 h-6" />,
          color: 'text-red-400',
          bgColor: 'bg-red-500',
          description: '适合网页嵌入，加载更快',
          fromFormat: '',
          toFormat: 'webm',
          fileType: 'VIDEO'
        },
        {
          id: 'video-computer',
          name: '视频转AVI',
          icon: <Monitor className="w-6 h-6" />,
          color: 'text-red-400',
          bgColor: 'bg-red-500',
          description: '适合电脑播放器播放',
          fromFormat: '',
          toFormat: 'avi',
          fileType: 'VIDEO'
        },
        {
          id: 'video-hd',
          name: '视频转MOV',
          icon: <Star className="w-6 h-6" />,
          color: 'text-red-400',
          bgColor: 'bg-red-500',
          description: '保持视频高清质量',
          fromFormat: '',
          toFormat: 'mov',
          fileType: 'VIDEO'
        },
        {
          id: 'video-stream',
          name: '视频转FLV',
          icon: <Radio className="w-6 h-6" />,
          color: 'text-red-400',
          bgColor: 'bg-red-500',
          description: '适合直播平台使用',
          fromFormat: '',
          toFormat: 'flv',
          fileType: 'VIDEO'
        }
      ]
    },
    {
      id: 'audio',
      name: '音频转换',
      icon: <FileAudio className="w-8 h-8" />,
      color: 'text-green-400',
      items: [
        {
          id: 'audio-mp3',
          name: '音频转MP3',
          icon: <Music className="w-6 h-6" />,
          color: 'text-green-400',
          bgColor: 'bg-green-500',
          description: '适合手机播放，文件较小',
          fromFormat: '',
          toFormat: 'mp3',
          fileType: 'AUDIO'
        },
        {
          id: 'audio-wav',
          name: '音频转WAV',
          icon: <Headphones className="w-6 h-6" />,
          color: 'text-green-400',
          bgColor: 'bg-green-500',
          description: '电脑播放，音质清晰',
          fromFormat: '',
          toFormat: 'wav',
          fileType: 'AUDIO'
        },
        {
          id: 'audio-flac',
          name: '音频转FLAC',
          icon: <Music className="w-6 h-6" />,
          color: 'text-green-400',
          bgColor: 'bg-green-500',
          description: '发烧友级别的音质体验',
          fromFormat: '',
          toFormat: 'flac',
          fileType: 'AUDIO'
        },
        {
          id: 'audio-ogg',
          name: '音频转OGG',
          icon: <Headphones className="w-6 h-6" />,
          color: 'text-green-400',
          bgColor: 'bg-green-500',
          description: '开放标准，兼容性好',
          fromFormat: '',
          toFormat: 'ogg',
          fileType: 'AUDIO'
        }
      ]
    },
    {
      id: 'image',
      name: '图片转换',
      icon: <FileImage className="w-8 h-8" />,
      color: 'text-blue-400',
      items: [
        {
          id: 'image-compress',
          name: '图片转JPG',
          icon: <Minimize2 className="w-6 h-6" />,
          color: 'text-blue-400',
          bgColor: 'bg-blue-500',
          description: '减小文件大小，节省空间',
          fromFormat: '',
          toFormat: 'jpg',
          fileType: 'IMAGE'
        },
        {
          id: 'image-share',
          name: '图片转WEBP',
          icon: <Share2 className="w-6 h-6" />,
          color: 'text-blue-400',
          bgColor: 'bg-blue-500',
          description: '适合微信、QQ等平台分享',
          fromFormat: '',
          toFormat: 'webp',
          fileType: 'IMAGE'
        },
        {
          id: 'image-hd',
          name: '图片转PNG',
          icon: <Eye className="w-6 h-6" />,
          color: 'text-blue-400',
          bgColor: 'bg-blue-500',
          description: '保持图片清晰度，无损压缩',
          fromFormat: '',
          toFormat: 'png',
          fileType: 'IMAGE'
        },
        {
          id: 'image-gif',
          name: '图片转GIF',
          icon: <Zap className="w-6 h-6" />,
          color: 'text-blue-400',
          bgColor: 'bg-blue-500',
          description: '制作简单的动态图片',
          fromFormat: '',
          toFormat: 'gif',
          fileType: 'IMAGE'
        },
        {          id: 'image-print',
          name: '图片转TIFF',
          icon: <Printer className="w-6 h-6" />,
          color: 'text-blue-400',
          bgColor: 'bg-blue-500',
          description: '适合高质量打印需求',
          fromFormat: '',
          toFormat: 'tiff',
          fileType: 'IMAGE'
        },
        {
          id: 'image-bmp',
          name: '图片转BMP',
          icon: <ImageIcon className="w-6 h-6" />,
          color: 'text-blue-400',
          bgColor: 'bg-blue-500',
          description: '无损位图格式，兼容性强',
          fromFormat: '',
          toFormat: 'bmp',
          fileType: 'IMAGE'
        }
      ]
    }
  ]

  const handleConversionClick = (item: CategoryItem) => {
    // Do not block navigation on HomePage; trial is enforced on ConversionPage
    setSelectedConversion({
      from: item.fromFormat,
      to: item.toFormat,
      fileType: item.fileType
    })
    setCurrentPage('conversion')
    trackMenuClick(item.name, item.id, item.id)
  }

  const handleBackToHome = () => {
    setCurrentPage('home')
    setSelectedConversion(null)
  }

  const handleSettingsClick = () => {
    setCurrentPage('settings')
  }

  if (currentPage === 'conversion' && selectedConversion) {
    return (
      <ConversionPage
        fromFormat={selectedConversion.from}
        toFormat={selectedConversion.to}
        fileType={selectedConversion.fileType}
        onBack={handleBackToHome}
      />
    )
  }

  if (currentPage === 'settings') {
    return <SettingsPage onBack={handleBackToHome} />
  }

  // Theme-specific classes
  const backgroundClasses = theme === 'dark'
    ? 'bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900'
    : 'bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100'

  const backgroundElementClasses = theme === 'dark' ? 'opacity-20' : 'opacity-25'
  const textClasses = theme === 'dark' ? 'text-white' : 'text-gray-800'
  const subtextClasses = theme === 'dark' ? 'text-gray-300' : 'text-gray-600'
  const cardClasses = theme === 'dark'
    ? 'bg-white bg-opacity-5 backdrop-blur-sm border-white border-opacity-10 hover:border-opacity-20'
    : 'bg-white bg-opacity-80 backdrop-blur-sm border-gray-200 border-opacity-50 hover:border-opacity-80'
  const itemCardClasses = theme === 'dark'
    ? 'bg-white bg-opacity-10 hover:bg-opacity-20 border-transparent hover:border-white hover:border-opacity-20'
    : 'bg-white bg-opacity-60 hover:bg-opacity-90 border-transparent hover:border-gray-300 hover:border-opacity-50'

  return (
    <div className={`min-h-screen ${backgroundClasses} p-4 relative overflow-hidden transition-all duration-500`}>
      {/* Static Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className={`absolute -top-40 -right-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl ${backgroundElementClasses} transition-opacity duration-500`}></div>
        <div className={`absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl ${backgroundElementClasses} transition-opacity duration-500`}></div>
        <div className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-10 transition-opacity duration-500`}></div>
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        {/* Header */}
        <div className="mb-6">
          <div className={`${cardClasses} rounded-xl p-4 border transition-all duration-300 flex items-center justify-between`}>
            {/* Left side - Title and Icon */}
            <div className="flex items-center">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl mr-3 shadow-glow flex items-center justify-center">
                <img className='w-full h-full' src={Logo} alt="" />
              </div>
              <div>
                <h1 className="text-2xl font-bold mb-0.5 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                  格式转换大师
                </h1>
                <p className={`${subtextClasses} text-sm`}>
                  支持视频、音频、图片格式之间的快速转换
                </p>
              </div>
            </div>

            {/* Right side - Settings Button */}
            <button
              onClick={handleSettingsClick}
              className={`w-10 h-10 rounded-lg ${theme === 'dark' ? 'bg-white bg-opacity-10 hover:bg-opacity-20 text-gray-300 hover:text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800'} flex items-center justify-center transition-all duration-300 hover:scale-110 group shadow-lg hover:shadow-glow-sm`}
              title="设置"
            >
              <Settings className="w-5 h-5 group-hover:rotate-90 transition-transform duration-300" />
            </button>
          </div>
        </div>

        {/* Categories */}
        <div className="space-y-4">
          {categories.map((category) => (
            <div
              key={category.id}
              className={`${cardClasses} rounded-xl p-4 border transition-all duration-300`}
            >
              {/* Category Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <div className={`${category.color} mr-2 transform hover:scale-110 transition-transform duration-300`}>
                    {category.icon}
                  </div>
                  <h2 className={`text-xl font-bold ${textClasses}`}>{category.name}</h2>
                </div>

                {/* Trial Status Badge */}
                {/* {!isVip && (
                  <div className="flex items-center space-x-2">
                    {trialStatus[category.id.toUpperCase() as TrialCategory] ? (
                      <div className="flex items-center space-x-1 px-2 py-1 bg-red-500 bg-opacity-20 border border-red-500 rounded-full">
                        <Lock className="w-3 h-3 text-red-400" />
                        <span className="text-red-400 text-xs font-medium">已试用</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-1 px-2 py-1 bg-green-500 bg-opacity-20 border border-green-500 rounded-full">
                        <span className="text-green-400 text-xs font-medium">可试用</span>
                      </div>
                    )}
                  </div>
                )}
                
                {isVip && (
                  <div className="flex items-center space-x-1 px-2 py-1 bg-yellow-500 bg-opacity-20 border border-yellow-500 rounded-full">
                    <Crown className="w-3 h-3 text-yellow-400" />
                    <span className="text-yellow-400 text-xs font-medium">VIP</span>
                  </div>
                )} */}
              </div>

              {/* Category Items */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                {category.items.map((item) => {
                  const categoryTrialUsed = !isVip && trialStatus[category.id.toUpperCase() as TrialCategory]

                  return (
                    <div
                      key={item.id}
                      onClick={() => handleConversionClick(item)}
                      className={`group cursor-pointer`}
                    >
                      <div className={`relative ${itemCardClasses} rounded-lg p-3 transition-all duration-400 hover:scale-103 hover:shadow-glow-sm border overflow-hidden`}>
                        {/* Shimmer effect on hover */}
                        <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-400">
                          <div className={`absolute inset-0 bg-gradient-to-r from-transparent ${theme === 'dark' ? 'via-white via-opacity-5' : 'via-gray-200 via-opacity-30'} to-transparent animate-shimmer`}></div>
                        </div>

                        <div className="flex items-center space-x-3 relative z-10">
                          {/* Icon */}
                          <div className={`w-10 h-10 ${item.bgColor} rounded-lg flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-all duration-300 shadow-lg group-hover:shadow-glow-sm`}>
                            <div className="text-white transform group-hover:rotate-12 transition-transform duration-300">
                              {item.icon}
                            </div>
                          </div>

                          {/* Text Content */}
                          <div className="flex-1 min-w-0">
                            <h3 className={`${textClasses} font-medium text-sm mb-1 truncate group-hover:text-blue-200 transition-colors duration-300`}>
                              {item.name}
                            </h3>
                            <p className={`${subtextClasses} text-xs truncate group-hover:text-gray-300 transition-colors duration-300`}>
                              {item.description}
                            </p>
                          </div>

                          {/* Arrow indicator */}
                          <div className="opacity-0 group-hover:opacity-100 transform translate-x-2 group-hover:translate-x-0 transition-all duration-300">
                            <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          ))}
        </div>

        {/* Footer Info */}
        <div className="mt-6 text-center">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 max-w-4xl mx-auto">
            <div className={`group flex items-center justify-center space-x-2 ${cardClasses} rounded-lg p-3 border transition-all duration-300 hover:scale-102 hover:shadow-glow-sm`}>
              <span className="text-2xl group-hover:scale-110 transition-transform duration-300">⚡</span>
              <span className={`${textClasses} font-medium group-hover:text-blue-200 transition-colors duration-300`}>极速转换</span>
            </div>
            <div className={`group flex items-center justify-center space-x-2 ${cardClasses} rounded-lg p-3 border transition-all duration-300 hover:scale-102 hover:shadow-glow-sm`}>
              <span className="text-2xl group-hover:scale-110 transition-transform duration-300">🔒</span>
              <span className={`${textClasses} font-medium group-hover:text-green-200 transition-colors duration-300`}>安全可靠</span>
            </div>
            <div className={`group flex items-center justify-center space-x-2 ${cardClasses} rounded-lg p-3 border transition-all duration-300 hover:scale-102 hover:shadow-glow-sm`}>
              <span className="text-2xl group-hover:scale-110 transition-transform duration-300">✨</span>
              <span className={`${textClasses} font-medium group-hover:text-purple-200 transition-colors duration-300`}>高质量输出</span>
            </div>
          </div>
        </div>
      </div>

      {/* Trial Limitation Modal removed from HomePage to avoid blocking navigation */}
    </div>
  )
}

export default HomePage